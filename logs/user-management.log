=== 2025-06-25 04:09:02 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [13540] using WatchFiles
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [13544]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-25 04:09:16.675 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-25 04:09:16.676 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 200 OK"
INFO:app.api.auth_endpoints:User <EMAIL> registered successfully without email verification
INFO:     127.0.0.1:55904 - "POST /api/v1/auth/register HTTP/1.1" 201 Created
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/authenticate "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/update-login?user_id=6983824c-9fa5-40bc-9d45-1d7c6cf7c70b&success=False "HTTP/1.1 200 OK"
WARNING:app.services.business.user_management.services.cluster_auth_service:Password verification failed for testuser991
INFO:     127.0.0.1:53938 - "POST /api/v1/auth/login HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:40208 - "POST /api/v1/auth/login HTTP/1.1" 422 Unprocessable Entity
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/authenticate "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/update-login?user_id=6983824c-9fa5-40bc-9d45-1d7c6cf7c70b&success=True "HTTP/1.1 200 OK"
INFO:app.services.business.user_management.services.cluster_auth_service:User authenticated successfully: <EMAIL>
INFO:app.api.auth_endpoints:User <EMAIL> logged in successfully
INFO:     127.0.0.1:52132 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
ERROR:app.services.business.user_management.services.cluster_auth_service:JWT Error: Invalid payload padding
INFO:     127.0.0.1:60526 - "POST /api/v1/auth/logout HTTP/1.1" 401 Unauthorized
ERROR:app.services.business.user_management.services.cluster_auth_service:JWT Error: Invalid payload padding
INFO:     127.0.0.1:34652 - "POST /api/v1/auth/logout HTTP/1.1" 401 Unauthorized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/authenticate "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/update-login?user_id=6983824c-9fa5-40bc-9d45-1d7c6cf7c70b&success=True "HTTP/1.1 200 OK"
INFO:app.services.business.user_management.services.cluster_auth_service:User authenticated successfully: <EMAIL>
INFO:app.api.auth_endpoints:User <EMAIL> logged in successfully
INFO:     127.0.0.1:55698 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
INFO:httpx:HTTP Request: GET http://localhost:7233/api/v1/users/6983824c-9fa5-40bc-9d45-1d7c6cf7c70b "HTTP/1.1 404 Not Found"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 404 - Not Found
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback: trying select_records for user_id: 6983824c-9fa5-40bc-9d45-1d7c6cf7c70b
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/insert "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - invalid input for query argument $3: '2025-06-25T04:57:58' (expected a datetime.date or datetime.datetime instance, got 'str')
ERROR:app.services.business.user_management.services.token_blacklist_service:❌ Failed to blacklist token: {'success': False, 'error': True, 'status_code': 500, 'error_detail': "invalid input for query argument $3: '2025-06-25T04:57:58' (expected a datetime.date or datetime.datetime instance, got 'str')", 'is_duplicate_key': False}
WARNING:app.api.auth_endpoints:⚠️ User <EMAIL> logged out - token blacklisting failed
INFO:     127.0.0.1:34096 - "POST /api/v1/auth/logout HTTP/1.1" 200 OK
INFO:     127.0.0.1:40374 - "POST /api/v1/auth/me HTTP/1.1" 405 Method Not Allowed
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/authenticate "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/update-login?user_id=6983824c-9fa5-40bc-9d45-1d7c6cf7c70b&success=True "HTTP/1.1 200 OK"
INFO:app.services.business.user_management.services.cluster_auth_service:User authenticated successfully: <EMAIL>
INFO:app.api.auth_endpoints:User <EMAIL> logged in successfully
INFO:     127.0.0.1:48678 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
INFO:     127.0.0.1:56216 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:56224 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56234 - "GET /docs HTTP/1.1" 200 OK
/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID test_authentication_system_api_v1_auth_test_get for function test_authentication_system at /home/<USER>/Documents/simba-micro-services/services/core/user-management/app/api/auth_endpoints.py
  warnings.warn(message, stacklevel=1)
INFO:     127.0.0.1:56234 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:50938 - "POST /api/v1/auth/me HTTP/1.1" 405 Method Not Allowed
INFO:     127.0.0.1:41428 - "PUT /api/v1/auth/me HTTP/1.1" 503 Service Unavailable
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [13544]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [24289]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-25 04:45:27.895 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-25 04:45:27.896 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: GET http://localhost:7233/api/v1/users/6983824c-9fa5-40bc-9d45-1d7c6cf7c70b "HTTP/1.1 404 Not Found"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 404 - Not Found
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback: trying select_records for user_id: 6983824c-9fa5-40bc-9d45-1d7c6cf7c70b
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://localhost:7233/api/v1/records/update "HTTP/1.1 404 Not Found"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 404 - Not Found
ERROR:app.api.auth_endpoints:Profile update error: 
INFO:     127.0.0.1:50602 - "PUT /api/v1/auth/me HTTP/1.1" 500 Internal Server Error
INFO:httpx:HTTP Request: GET http://localhost:7233/api/v1/users/6983824c-9fa5-40bc-9d45-1d7c6cf7c70b "HTTP/1.1 404 Not Found"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 404 - Not Found
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback: trying select_records for user_id: 6983824c-9fa5-40bc-9d45-1d7c6cf7c70b
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:     127.0.0.1:42268 - "PUT /api/v1/auth/me HTTP/1.1" 422 Unprocessable Entity
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [24289]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [26484]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-25 04:49:54.907 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-25 04:49:54.908 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     127.0.0.1:35718 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:35718 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35728 - "GET /docs HTTP/1.1" 200 OK
/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID test_authentication_system_api_v1_auth_test_get for function test_authentication_system at /home/<USER>/Documents/simba-micro-services/services/core/user-management/app/api/auth_endpoints.py
  warnings.warn(message, stacklevel=1)
INFO:     127.0.0.1:35728 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:52712 - "GET /health HTTP/1.1" 200 OK
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/authenticate "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/update-login?user_id=5ab59205-a551-4c58-b641-a4ea727a8905&success=False "HTTP/1.1 200 OK"
WARNING:app.services.business.user_management.services.cluster_auth_service:Password verification failed for testuser54
INFO:     127.0.0.1:47226 - "POST /api/v1/auth/login HTTP/1.1" 401 Unauthorized
