=== 2025-06-25 04:09:45 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7309 (Press CTRL+C to quit)
INFO:     Started reloader process [13899] using WatchFiles
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 58, in <module>
    @rate_limit_payment
     ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/security/middleware.py", line 250, in rate_limit_payment
    return limiter.limit(SECURITY_CONFIG["rate_limits"]["payment"])(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/slowapi/extension.py", line 713, in decorator
    raise Exception(
Exception: No "request" or "websocket" argument on function "<function require_permission.<locals>.decorator.<locals>.wrapper at 0x7fee0814a340>"
WARNING:  WatchFiles detected changes in 'app/security/auth.py'. Reloading...
Process SpawnProcess-2:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 58, in <module>
    @rate_limit_payment
     ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/security/middleware.py", line 250, in rate_limit_payment
    return limiter.limit(SECURITY_CONFIG["rate_limits"]["payment"])(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/slowapi/extension.py", line 713, in decorator
    raise Exception(
Exception: No "request" or "websocket" argument on function "<function require_permission.<locals>.decorator.<locals>.wrapper at 0x7fc033352340>"
WARNING:  WatchFiles detected changes in 'app/core/migrations.py'. Reloading...
Process SpawnProcess-3:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 58, in <module>
    @rate_limit_payment
     ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/security/middleware.py", line 250, in rate_limit_payment
    return limiter.limit(SECURITY_CONFIG["rate_limits"]["payment"])(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/slowapi/extension.py", line 713, in decorator
    raise Exception(
Exception: No "request" or "websocket" argument on function "<function require_permission.<locals>.decorator.<locals>.wrapper at 0x7fa880746340>"
=== 2025-06-25 06:19:13 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7309 (Press CTRL+C to quit)
INFO:     Started reloader process [37224] using WatchFiles
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 58, in <module>
    @rate_limit_payment
     ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/security/middleware.py", line 250, in rate_limit_payment
    return limiter.limit(SECURITY_CONFIG["rate_limits"]["payment"])(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/slowapi/extension.py", line 713, in decorator
    raise Exception(
Exception: No "request" or "websocket" argument on function "<function require_permission.<locals>.decorator.<locals>.wrapper at 0x7f699cb4e340>"
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
Process SpawnProcess-4:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 59, in <module>
    @rate_limit_payment
     ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/security/middleware.py", line 250, in rate_limit_payment
    return limiter.limit(SECURITY_CONFIG["rate_limits"]["payment"])(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/slowapi/extension.py", line 713, in decorator
    raise Exception(
Exception: No "request" or "websocket" argument on function "<function require_permission.<locals>.decorator.<locals>.wrapper at 0x7fe0b5f42ca0>"
Process SpawnProcess-2:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 59, in <module>
    @rate_limit_payment
     ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/security/middleware.py", line 250, in rate_limit_payment
    return limiter.limit(SECURITY_CONFIG["rate_limits"]["payment"])(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/slowapi/extension.py", line 713, in decorator
    raise Exception(
Exception: No "request" or "websocket" argument on function "<function require_permission.<locals>.decorator.<locals>.wrapper at 0x7f61e3d46ca0>"
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
Process SpawnProcess-5:
Process SpawnProcess-3:
Traceback (most recent call last):
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 111, in <module>
    @rate_limit_default
     ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/security/middleware.py", line 262, in rate_limit_default
    return limiter.limit(SECURITY_CONFIG["rate_limits"]["default"])(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/slowapi/extension.py", line 713, in decorator
    raise Exception(
Exception: No "request" or "websocket" argument on function "<function require_permission.<locals>.decorator.<locals>.wrapper at 0x7f0219342f20>"
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 111, in <module>
    @rate_limit_default
     ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/security/middleware.py", line 262, in rate_limit_default
    return limiter.limit(SECURITY_CONFIG["rate_limits"]["default"])(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/slowapi/extension.py", line 713, in decorator
    raise Exception(
Exception: No "request" or "websocket" argument on function "<function require_permission.<locals>.decorator.<locals>.wrapper at 0x7ff333942e80>"
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
Process SpawnProcess-6:
Process SpawnProcess-4:
Traceback (most recent call last):
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 284, in <module>
    @rate_limit_default
     ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/security/middleware.py", line 262, in rate_limit_default
    return limiter.limit(SECURITY_CONFIG["rate_limits"]["default"])(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/slowapi/extension.py", line 713, in decorator
    raise Exception(
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
Exception: No "request" or "websocket" argument on function "<function require_permission.<locals>.decorator.<locals>.wrapper at 0x7fd61c54b880>"
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 284, in <module>
    @rate_limit_default
     ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/security/middleware.py", line 262, in rate_limit_default
    return limiter.limit(SECURITY_CONFIG["rate_limits"]["default"])(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/slowapi/extension.py", line 713, in decorator
    raise Exception(
Exception: No "request" or "websocket" argument on function "<function require_permission.<locals>.decorator.<locals>.wrapper at 0x7f3a57b47880>"
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-5:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 35, in <module>
    from app.core.monitoring import get_metrics, get_health_check, update_system_metrics_task
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/core/monitoring.py", line 8, in <module>
    import psutil
ModuleNotFoundError: No module named 'psutil'
Process SpawnProcess-7:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 35, in <module>
    from app.core.monitoring import get_metrics, get_health_check, update_system_metrics_task
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/core/monitoring.py", line 8, in <module>
    import psutil
ModuleNotFoundError: No module named 'psutil'
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-8:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 35, in <module>
    from app.core.monitoring import get_metrics, get_health_check, update_system_metrics_task
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/core/monitoring.py", line 8, in <module>
    import psutil
ModuleNotFoundError: No module named 'psutil'
Process SpawnProcess-6:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 35, in <module>
    from app.core.monitoring import get_metrics, get_health_check, update_system_metrics_task
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/core/monitoring.py", line 8, in <module>
    import psutil
ModuleNotFoundError: No module named 'psutil'
INFO:     Stopping reloader process [37224]
WARNING:  WatchFiles detected changes in '.venv/lib64/python3.11/site-packages/psutil/tests/test_process_all.py', '.venv/lib64/python3.11/site-packages/psutil/tests/__init__.py', '.venv/lib64/python3.11/site-packages/psutil/tests/test_windows.py', '.venv/lib64/python3.11/site-packages/psutil/__init__.py', '.venv/lib64/python3.11/site-packages/psutil/_psosx.py', '.venv/lib64/python3.11/site-packages/psutil/_psbsd.py', '.venv/lib64/python3.11/site-packages/psutil/tests/test_posix.py', '.venv/lib64/python3.11/site-packages/psutil/tests/test_memleaks.py', '.venv/lib64/python3.11/site-packages/psutil/_common.py', '.venv/lib64/python3.11/site-packages/psutil/_pswindows.py', '.venv/lib64/python3.11/site-packages/psutil/tests/test_bsd.py', '.venv/lib64/python3.11/site-packages/psutil/tests/test_scripts.py', '.venv/lib64/python3.11/site-packages/psutil/tests/test_sunos.py', '.venv/lib64/python3.11/site-packages/psutil/tests/test_contracts.py', '.venv/lib64/python3.11/site-packages/psutil/tests/test_aix.py', '.venv/lib64/python3.11/site-packages/psutil/_psposix.py', '.venv/lib64/python3.11/site-packages/psutil/_pssunos.py', '.venv/lib64/python3.11/site-packages/psutil/tests/test_process.py', '.venv/lib64/python3.11/site-packages/psutil/tests/test_misc.py', '.venv/lib64/python3.11/site-packages/psutil/tests/test_linux.py', '.venv/lib64/python3.11/site-packages/psutil/_pslinux.py', '.venv/lib64/python3.11/site-packages/psutil/tests/__main__.py', '.venv/lib64/python3.11/site-packages/psutil/tests/test_system.py', '.venv/lib64/python3.11/site-packages/psutil/_psaix.py', '.venv/lib64/python3.11/site-packages/psutil/tests/test_connections.py', '.venv/lib64/python3.11/site-packages/psutil/tests/test_unicode.py', '.venv/lib64/python3.11/site-packages/psutil/tests/test_osx.py', '.venv/lib64/python3.11/site-packages/psutil/tests/test_testutils.py'. Reloading...
=== 2025-06-25 06:28:36 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7309 (Press CTRL+C to quit)
INFO:     Started reloader process [42578] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-9:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 39, in <module>
    from app.core.migrations import initialize_database_schema
ModuleNotFoundError: No module named 'app.core.migrations'
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 39, in <module>
    from app.core.migrations import initialize_database_schema
ModuleNotFoundError: No module named 'app.core.migrations'
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [42765]
INFO:     Waiting for application startup.
2025-06-25 06:29:50.811 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:29:50.811 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [42766]
INFO:     Waiting for application startup.
2025-06-25 06:29:50.841 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:29:50.842 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:29:51.027 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:29:51.029 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:29:51.149 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:29:51.150 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:29:51.150 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:29:51.151 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
{"event": "\u274c Database initialization failed: name 'initialize_database' is not defined", "logger": "main", "level": "error", "timestamp": "2025-06-25T06:29:51.152161Z"}
{"event": "\u26a0\ufe0f Continuing without database - some features may be limited", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:29:51.152652Z"}
2025-06-25 06:29:51.152 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:29:51.153 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
{"event": "\u274c Database initialization failed: name 'initialize_database' is not defined", "logger": "main", "level": "error", "timestamp": "2025-06-25T06:29:51.153888Z"}
{"event": "\u26a0\ufe0f Continuing without database - some features may be limited", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:29:51.154467Z"}
2025-06-25 06:29:51.422 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:29:51.423 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:29:51.426 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:29:51.427 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:30:00.725401Z"}
INFO:     127.0.0.1:57420 - "GET /health HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
{"event": "\u26a0\ufe0f Database cleanup failed: name 'cleanup_database' is not defined", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:30:28.852146Z"}
INFO:     Application shutdown complete.
INFO:     Finished server process [42766]
INFO:     Waiting for application shutdown.
{"event": "\u26a0\ufe0f Database cleanup failed: name 'cleanup_database' is not defined", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:30:28.857059Z"}
INFO:     Application shutdown complete.
INFO:     Finished server process [42765]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-3:
Process SpawnProcess-11:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 39, in <module>
    from app.core.migrations import initialize_database_schema
ModuleNotFoundError: No module named 'app.core.migrations'
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 39, in <module>
    from app.core.migrations import initialize_database_schema
ModuleNotFoundError: No module named 'app.core.migrations'
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [43874]
INFO:     Waiting for application startup.
2025-06-25 06:36:46.191 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:36:46.192 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [43875]
INFO:     Waiting for application startup.
2025-06-25 06:36:46.327 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:36:46.328 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:36:46.452 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:36:46.522 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:36:46.523 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:36:46.523 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
{"event": "\u274c Database initialization failed: name 'initialize_database' is not defined", "logger": "main", "level": "error", "timestamp": "2025-06-25T06:36:46.524486Z"}
{"event": "\u26a0\ufe0f Continuing without database - some features may be limited", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:36:46.525128Z"}
2025-06-25 06:36:46.526 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:36:46.591 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:36:46.592 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:36:46.593 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
{"event": "\u274c Database initialization failed: name 'initialize_database' is not defined", "logger": "main", "level": "error", "timestamp": "2025-06-25T06:36:46.594001Z"}
{"event": "\u26a0\ufe0f Continuing without database - some features may be limited", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:36:46.594621Z"}
2025-06-25 06:36:51.627 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:36:51.628 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:36:51.699 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:36:51.701 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:36:53.058126Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:36:53.166335Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:36:53.273460Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:36:53.382023Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:36:53.488798Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:36:53.597648Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:36:53.705039Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:36:53.814088Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:36:53.923118Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:36:54.031784Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:36:54.140234Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:36:54.248924Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:36:54.357292Z"}
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
{"event": "\u26a0\ufe0f Database cleanup failed: name 'cleanup_database' is not defined", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:36:56.747411Z"}
INFO:     Application shutdown complete.
INFO:     Finished server process [43874]
INFO:     Waiting for application shutdown.
{"event": "\u26a0\ufe0f Database cleanup failed: name 'cleanup_database' is not defined", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:36:56.772599Z"}
INFO:     Application shutdown complete.
INFO:     Finished server process [43875]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [43918]
INFO:     Waiting for application startup.
2025-06-25 06:37:02.123 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:37:02.125 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [43919]
INFO:     Waiting for application startup.
2025-06-25 06:37:02.195 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:37:02.196 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:37:02.321 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:37:02.388 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:37:02.390 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:37:02.390 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:37:02.392 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:37:02.452 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:37:02.453 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:37:02.454 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:37:07.506 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:37:07.510 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:37:07.565 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:37:07.566 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:37:08.975399Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:37:09.082355Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:37:09.187040Z"}
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
{"event": "\u26a0\ufe0f Database cleanup failed: name 'cleanup_database' is not defined", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:37:20.863186Z"}
INFO:     Application shutdown complete.
INFO:     Finished server process [43918]
INFO:     Waiting for application shutdown.
{"event": "\u26a0\ufe0f Database cleanup failed: name 'cleanup_database' is not defined", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:37:20.934335Z"}
INFO:     Application shutdown complete.
INFO:     Finished server process [43919]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [44113]
INFO:     Waiting for application startup.
2025-06-25 06:37:26.545 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:37:26.546 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [44114]
INFO:     Waiting for application startup.
2025-06-25 06:37:26.554 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:37:26.555 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:37:26.826 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:37:26.829 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:37:26.916 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:37:26.917 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:37:26.918 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:37:26.937 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:37:26.938 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:37:26.939 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:37:32.065 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:37:32.067 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:37:32.072 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:37:32.074 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:37:33.436331Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:37:33.541378Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:37:35.315411Z"}
INFO:     127.0.0.1:48008 - "GET /health HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/postgresql_client.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/postgresql_client.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [44113]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [44114]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [44482]
INFO:     Waiting for application startup.
2025-06-25 06:38:18.746 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:38:18.747 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [44483]
INFO:     Waiting for application startup.
2025-06-25 06:38:18.756 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:38:18.757 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:38:19.172 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:38:19.287 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:38:19.404 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:38:19.406 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:38:19.407 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:38:19.465 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:38:19.467 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:38:19.470 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
2025-06-25 06:38:24.851 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:38:24.854 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:38:24.862 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:38:24.863 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
ERROR:    Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/routing.py", line 686, in lifespan
    await receive()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/lifespan/on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/queues.py", line 158, in get
    await getter
asyncio.exceptions.CancelledError

ERROR:    Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/routing.py", line 686, in lifespan
    await receive()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/lifespan/on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/queues.py", line 158, in get
    await getter
asyncio.exceptions.CancelledError

ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/fastapi/applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 83, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/security/middleware.py", line 80, in __call__
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 96, in receive
    await checkpoint()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/anyio/lowlevel.py", line 33, in checkpoint
    await get_asynclib().checkpoint()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/anyio/_backends/_asyncio.py", line 447, in checkpoint
    await sleep(0)
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/tasks.py", line 640, in sleep
    await __sleep0()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/tasks.py", line 634, in __sleep0
    yield
asyncio.exceptions.CancelledError
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:38:26.557156Z"}
Process SpawnProcess-16:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 63, in <module>
    db: Session = Depends(get_db),
                          ^^^^^^
NameError: name 'get_db' is not defined
Process SpawnProcess-8:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 63, in <module>
    db: Session = Depends(get_db),
                          ^^^^^^
NameError: name 'get_db' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
Process SpawnProcess-9:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 63, in <module>
    db: Session = Depends(get_db),
                          ^^^^^^
NameError: name 'get_db' is not defined
Process SpawnProcess-17:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 63, in <module>
    db: Session = Depends(get_db),
                          ^^^^^^
NameError: name 'get_db' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
Process SpawnProcess-10:
Process SpawnProcess-18:
Traceback (most recent call last):
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 115, in <module>
    db: Session = Depends(get_db),
                          ^^^^^^
NameError: name 'get_db' is not defined
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 115, in <module>
    db: Session = Depends(get_db),
                          ^^^^^^
NameError: name 'get_db' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
Process SpawnProcess-11:
Process SpawnProcess-19:
Traceback (most recent call last):
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 130, in <module>
    db: Session = Depends(get_db),
                          ^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
NameError: name 'get_db' is not defined
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 130, in <module>
    db: Session = Depends(get_db),
                          ^^^^^^
NameError: name 'get_db' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
Process SpawnProcess-20:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 301, in <module>
    db: Session = Depends(get_db),
                          ^^^^^^
NameError: name 'get_db' is not defined
Process SpawnProcess-12:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 301, in <module>
    db: Session = Depends(get_db),
                          ^^^^^^
NameError: name 'get_db' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
Process SpawnProcess-21:
Traceback (most recent call last):
Process SpawnProcess-13:
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 332, in <module>
    db: Session = Depends(get_db),
                          ^^^^^^
NameError: name 'get_db' is not defined
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 332, in <module>
    db: Session = Depends(get_db),
                          ^^^^^^
NameError: name 'get_db' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [57005]
INFO:     Waiting for application startup.
2025-06-25 06:41:25.509 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:41:25.510 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [57006]
INFO:     Waiting for application startup.
2025-06-25 06:41:25.512 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:41:25.512 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:41:25.756 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:41:25.758 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:41:25.883 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:41:25.883 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:41:25.884 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:41:25.885 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:41:25.885 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:41:25.885 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:41:31.071 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:41:31.072 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:41:31.077 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:41:31.078 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:41:32.581054Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:41:32.689115Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:41:32.800564Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:41:32.912164Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:41:33.023234Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:41:33.134107Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:41:33.243293Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:41:39.854661Z"}
INFO:     127.0.0.1:38916 - "GET /health HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57005]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57006]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [57093]
INFO:     Waiting for application startup.
2025-06-25 06:41:56.241 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:41:56.242 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [57092]
INFO:     Waiting for application startup.
2025-06-25 06:41:56.431 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:41:56.432 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:41:56.433 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:41:56.494 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:41:56.495 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:41:56.496 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:41:56.607 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:41:56.663 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:41:56.665 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:41:56.666 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:42:01.602 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:42:01.603 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:42:01.777 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:42:01.779 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:42:03.010371Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:42:03.118178Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:42:13.966826Z"}
INFO:     127.0.0.1:53848 - "GET /health HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57092]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57093]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [57234]
INFO:     Waiting for application startup.
2025-06-25 06:42:29.545 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:42:29.546 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [57235]
INFO:     Waiting for application startup.
2025-06-25 06:42:29.643 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:42:29.644 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:42:29.741 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:42:29.815 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:42:29.818 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:42:29.819 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:42:29.832 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:42:29.906 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:42:29.908 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:42:29.908 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:42:34.935 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:42:34.936 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:42:35.048 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:42:35.050 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:42:36.433963Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:42:36.538480Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:42:47.916273Z"}
INFO:     127.0.0.1:34730 - "GET /health HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/core/config.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/core/config.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57234]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57235]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [57403]
INFO:     Waiting for application startup.
2025-06-25 06:43:06.998 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:43:06.999 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [57402]
INFO:     Waiting for application startup.
2025-06-25 06:43:07.088 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:43:07.089 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:43:07.162 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:43:07.220 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:43:07.222 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:43:07.223 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:43:07.259 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:43:07.325 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:43:07.327 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:43:07.327 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
WARNING:  WatchFiles detected changes in 'app/services/postgresql_client.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/postgresql_client.py'. Reloading...
2025-06-25 06:43:12.322 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:43:12.323 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:43:12.428 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:43:12.432 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
ERROR:    Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/routing.py", line 686, in lifespan
    await receive()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/lifespan/on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/queues.py", line 158, in get
    await getter
asyncio.exceptions.CancelledError

ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/fastapi/applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 83, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/security/middleware.py", line 80, in __call__
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 96, in receive
    await checkpoint()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/anyio/lowlevel.py", line 33, in checkpoint
    await get_asynclib().checkpoint()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/anyio/_backends/_asyncio.py", line 447, in checkpoint
    await sleep(0)
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/tasks.py", line 640, in sleep
    await __sleep0()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/tasks.py", line 634, in __sleep0
    yield
asyncio.exceptions.CancelledError
INFO:     Application startup complete.
ERROR:    Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/routing.py", line 686, in lifespan
    await receive()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/lifespan/on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/queues.py", line 158, in get
    await getter
asyncio.exceptions.CancelledError

/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [57429]
INFO:     Waiting for application startup.
2025-06-25 06:43:19.253 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:43:19.254 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [57428]
INFO:     Waiting for application startup.
2025-06-25 06:43:19.281 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:43:19.282 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:43:19.454 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:43:19.481 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:43:19.523 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:43:19.525 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:43:19.526 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:43:19.548 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:43:19.549 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:43:19.550 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:43:24.672 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:43:24.674 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:43:24.681 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:43:24.683 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/postgresql_client.py'. Reloading...
INFO:     Shutting down
WARNING:  WatchFiles detected changes in 'app/services/postgresql_client.py'. Reloading...
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:43:26.117235Z"}
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57429]
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:43:26.225207Z"}
INFO:     Shutting down
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:43:26.330755Z"}
INFO:     Waiting for background tasks to complete. (CTRL+C to force quit)
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57428]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [57535]
INFO:     Waiting for application startup.
2025-06-25 06:43:32.074 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:43:32.075 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [57536]
INFO:     Waiting for application startup.
2025-06-25 06:43:32.092 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:43:32.093 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:43:32.252 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:43:32.254 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:43:32.357 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:43:32.357 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:43:32.358 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:43:32.358 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:43:32.359 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:43:32.359 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:43:37.491 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:43:37.492 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:43:37.496 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:43:37.497 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:43:38.821215Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:43:38.927800Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:43:56.504624Z"}
INFO:     127.0.0.1:47324 - "GET /health HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/cluster_db_init.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/cluster_db_init.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57536]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57535]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [57629]
INFO:     Waiting for application startup.
2025-06-25 06:44:15.141 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:44:15.143 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [57630]
INFO:     Waiting for application startup.
2025-06-25 06:44:15.220 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:44:15.221 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:44:15.392 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:44:15.474 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:44:15.514 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:44:15.517 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:44:15.518 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:44:15.619 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:44:15.622 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:44:15.623 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:44:20.772 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:44:20.774 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:44:20.779 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:44:20.781 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:44:22.108438Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:44:22.214564Z"}
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57630]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57629]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [57714]
INFO:     Waiting for application startup.
2025-06-25 06:44:32.094 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:44:32.095 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [57713]
INFO:     Waiting for application startup.
2025-06-25 06:44:32.238 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:44:32.239 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:44:32.270 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:44:32.344 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:44:32.346 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:44:32.346 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:44:32.414 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:44:32.473644Z"}
2025-06-25 06:44:32.498 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:44:32.499 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:44:32.500 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:44:32.587824Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:44:34.553212Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:44:34.654228Z"}
2025-06-25 06:44:37.543 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:44:37.544 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:44:37.636 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:44:37.637 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:44:38.600693Z"}
INFO:     Application startup complete.
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:44:39.011058Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:44:39.118824Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:44:39.259044Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:44:39.274397Z"}
WARNING:  WatchFiles detected changes in 'app/models/database.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/models/database.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57714]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57713]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [57743]
INFO:     Waiting for application startup.
2025-06-25 06:44:48.226 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:44:48.228 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [57744]
INFO:     Waiting for application startup.
2025-06-25 06:44:48.298 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:44:48.299 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:44:48.540 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:44:48.556 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:44:48.660 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:44:48.662 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:44:48.663 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:44:48.692 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:44:48.694 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:44:48.695 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:44:48.813392Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:44:48.837915Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:44:50.883191Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:44:50.888552Z"}
2025-06-25 06:44:53.931 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:44:53.932 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:44:53.933 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:44:53.933 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:44:54.979127Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:44:55.329635Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:44:55.437473Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:44:55.452890Z"}
WARNING:  WatchFiles detected changes in 'app/models/database.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/models/database.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57744]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57743]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [57827]
INFO:     Waiting for application startup.
2025-06-25 06:45:03.031 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:45:03.032 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:45:03.234 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
INFO:     Started server process [57826]
INFO:     Waiting for application startup.
2025-06-25 06:45:03.303 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:45:03.304 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:45:03.325 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:45:03.327 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:45:03.327 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:03.417609Z"}
2025-06-25 06:45:03.491 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:45:03.565 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:45:03.567 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:45:03.568 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:03.725145Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:05.467975Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:05.774401Z"}
2025-06-25 06:45:08.475 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:45:08.476 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:45:08.768 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:45:08.770 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:09.860571Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:45:09.968661Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:45:10.076286Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:45:10.186250Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:10.199169Z"}
INFO:     127.0.0.1:32912 - "GET /health HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/models/database.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/models/database.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57826]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57827]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [57891]
INFO:     Waiting for application startup.
2025-06-25 06:45:16.793 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:45:16.794 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [57890]
INFO:     Waiting for application startup.
2025-06-25 06:45:16.838 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:45:16.839 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:45:16.959 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:45:17.000 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:45:17.024 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:45:17.025 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:45:17.026 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:45:17.061 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:45:17.063 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:45:17.063 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:17.119098Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:17.155727Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:19.175930Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:19.204099Z"}
2025-06-25 06:45:22.223 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:45:22.224 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:45:22.229 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:45:22.231 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:23.292196Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:45:23.648885Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:45:23.753694Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:23.763441Z"}
WARNING:  WatchFiles detected changes in 'app/models/database.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/models/database.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57890]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57891]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [57974]
INFO:     Waiting for application startup.
2025-06-25 06:45:34.084 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:45:34.085 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [57975]
2025-06-25 06:45:34.279 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
INFO:     Waiting for application startup.
2025-06-25 06:45:34.281 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:45:34.281 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:45:34.345 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:45:34.347 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:45:34.348 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:34.444235Z"}
2025-06-25 06:45:34.450 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:45:34.588 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:45:34.591 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:45:34.592 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:34.781931Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:36.506011Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:36.823253Z"}
2025-06-25 06:45:39.534 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:45:39.535 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:45:39.840 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:45:39.842 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:40.587402Z"}
INFO:     Application startup complete.
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:45:41.252262Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:45:41.358667Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:45:41.468226Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:41.480685Z"}
INFO:     127.0.0.1:44722 - "GET /health HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/models/database.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/models/database.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57974]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [57975]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [58005]
INFO:     Waiting for application startup.
2025-06-25 06:45:50.759 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:45:50.759 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [58004]
INFO:     Waiting for application startup.
2025-06-25 06:45:50.846 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:45:50.846 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:45:50.943 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:45:51.025 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:45:51.026 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:45:51.026 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:45:51.049 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:45:51.174 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:45:51.177 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:45:51.180 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:51.186060Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:51.317325Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:53.237736Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:53.372586Z"}
2025-06-25 06:45:56.276 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:45:56.277 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:45:56.402 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:45:56.403 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:57.459979Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:45:57.719580Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:45:57.825644Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:45:57.834850Z"}
WARNING:  WatchFiles detected changes in 'app/models/database.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/models/database.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [58004]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [58005]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [58087]
INFO:     Waiting for application startup.
2025-06-25 06:46:07.447 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:46:07.448 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [58088]
INFO:     Waiting for application startup.
2025-06-25 06:46:07.483 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:46:07.484 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:46:07.610 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:46:07.648 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:46:07.676 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:46:07.677 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:46:07.678 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:46:07.718 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:46:07.719 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:46:07.719 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:46:07.778581Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:46:07.808069Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:46:09.845783Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:46:09.880419Z"}
2025-06-25 06:46:12.870 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:46:12.871 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:46:12.875 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:46:12.876 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:46:13.941632Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:46:14.302755Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:46:14.408291Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:46:14.521257Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:46:14.529770Z"}
INFO:     127.0.0.1:45592 - "GET /health HTTP/1.1" 200 OK
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:46:22.005197Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:46:22.590951Z"}
WARNING:  WatchFiles detected changes in 'app/models/database.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/models/database.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [58087]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [58088]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [58172]
INFO:     Waiting for application startup.
2025-06-25 06:46:29.490 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:46:29.491 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [58173]
INFO:     Waiting for application startup.
2025-06-25 06:46:29.513 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:46:29.514 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:46:29.745 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:46:29.758 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:46:29.808 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:46:29.810 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:46:29.810 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:46:29.824 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:46:29.826 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:46:29.827 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:46:29.899288Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:46:29.947646Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:46:31.993207Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:46:32.031087Z"}
2025-06-25 06:46:34.962 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:46:34.963 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:46:35.011 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:46:35.012 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:46:36.079846Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:46:36.462130Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:46:36.569831Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:46:36.617840Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:46:44.129192Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:46:44.662011Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:46:48.171234Z"}
INFO:     127.0.0.1:57922 - "GET /health HTTP/1.1" 200 OK
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 5 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:47:00.194792Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 5 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:47:00.732852Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:47:21.891294Z"}
INFO:     127.0.0.1:51382 - "GET /health HTTP/1.1" 200 OK
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 6 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:47:32.232264Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 6 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:47:32.773043Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:47:55.978241Z"}
INFO:     127.0.0.1:44510 - "GET /health HTTP/1.1" 200 OK
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:48:29.791404Z"}
INFO:     127.0.0.1:56532 - "GET /health HTTP/1.1" 200 OK
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 7 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:48:36.275931Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 7 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:48:36.818856Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:49:03.415698Z"}
INFO:     127.0.0.1:36034 - "GET /health HTTP/1.1" 200 OK
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:49:36.913206Z"}
INFO:     127.0.0.1:55782 - "GET /health HTTP/1.1" 200 OK
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:50:10.686435Z"}
INFO:     127.0.0.1:34250 - "GET /health HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
INFO:     Shutting down
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [58172]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [58173]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [58935]
INFO:     Waiting for application startup.
2025-06-25 06:50:26.319 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:50:26.319 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [58940]
INFO:     Waiting for application startup.
2025-06-25 06:50:26.332 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:50:26.333 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:50:26.506 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:50:26.518 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:50:26.613 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:50:26.615 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:50:26.616 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:50:26.637 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:50:26.639 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:50:26.641 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:50:26.745758Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:50:26.762943Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:50:28.807785Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:50:28.834222Z"}
2025-06-25 06:50:31.921 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:50:31.922 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:50:31.930 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:50:31.931 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:50:33.006682Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:50:33.624108Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:50:33.731604Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:50:33.753659Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:50:41.052981Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:50:41.807151Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:50:44.405414Z"}
INFO:     127.0.0.1:39426 - "GET /health HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [58935]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [58940]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [59033]
INFO:     Waiting for application startup.
2025-06-25 06:50:59.864 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:50:59.865 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [59034]
INFO:     Waiting for application startup.
2025-06-25 06:50:59.995 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:50:59.996 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:51:00.137 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:51:00.369 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:51:00.372 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:51:00.373 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:51:00.373 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:51:00.485 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:51:00.490 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:51:00.492 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:51:00.525822Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:51:00.605278Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:51:02.581483Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:51:02.664672Z"}
2025-06-25 06:51:05.596 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:51:05.597 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:51:05.686 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:51:05.688 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:51:06.669782Z"}
INFO:     Application startup complete.
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:51:07.167898Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:51:07.273180Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:51:07.290203Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:51:14.720752Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:51:15.342071Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:51:18.172177Z"}
INFO:     127.0.0.1:60868 - "GET /health HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/billing.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [59033]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [59034]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [59150]
INFO:     Waiting for application startup.
2025-06-25 06:51:31.189 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:51:31.191 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [59149]
INFO:     Waiting for application startup.
2025-06-25 06:51:31.355 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:51:31.356 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:51:31.431 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:51:31.521 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:51:31.522 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:51:31.523 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:51:31.579 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:51:31.646897Z"}
2025-06-25 06:51:31.676 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:51:31.678 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:51:31.679 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:51:31.789455Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:51:33.714255Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:51:33.847954Z"}
2025-06-25 06:51:36.729 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:51:36.731 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:51:36.857 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:51:36.859 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:51:37.916062Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:51:38.220764Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:51:38.327200Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:51:38.344335Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:51:45.987807Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:51:46.400857Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:51:51.841821Z"}
INFO:     127.0.0.1:46262 - "GET /health HTTP/1.1" 200 OK
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 5 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:52:02.039625Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 5 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:52:02.452900Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:52:25.914354Z"}
INFO:     127.0.0.1:39692 - "GET /health HTTP/1.1" 200 OK
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 6 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:52:34.088585Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 6 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:52:34.496548Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:52:59.921206Z"}
INFO:     127.0.0.1:34982 - "GET /health HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/postgresql_client.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/postgresql_client.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [59150]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [59149]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [59499]
INFO:     Waiting for application startup.
2025-06-25 06:53:35.755 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:53:35.756 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [59498]
INFO:     Waiting for application startup.
2025-06-25 06:53:35.761 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:53:35.762 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:53:35.928 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:53:35.939 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:53:36.046 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:53:36.047 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:53:36.048 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:53:36.066 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:53:36.068 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:53:36.069 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:53:36.183484Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:53:36.219991Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:53:38.236116Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:53:38.269672Z"}
2025-06-25 06:53:41.322 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:53:41.324 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:53:41.328 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:53:41.329 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:53:42.418316Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:53:42.870241Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:53:42.975679Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:53:43.087467Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:53:43.095487Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:53:50.461092Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:53:51.139294Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 5 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:54:06.515985Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 5 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:54:07.182060Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:54:08.684101Z"}
INFO:     127.0.0.1:55758 - "GET /health HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/billing_service.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/billing_service.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [59499]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [59498]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [59621]
INFO:     Waiting for application startup.
2025-06-25 06:54:27.916 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:54:27.918 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [59622]
INFO:     Waiting for application startup.
2025-06-25 06:54:28.092 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:54:28.093 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:54:28.115 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:54:28.190 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:54:28.192 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:54:28.193 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:54:28.317 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:54:28.331348Z"}
2025-06-25 06:54:28.394 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:54:28.396 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:54:28.397 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:54:28.494822Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:54:30.393969Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:54:30.556421Z"}
2025-06-25 06:54:33.417 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:54:33.419 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:54:33.560 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:54:33.561 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:54:34.475558Z"}
INFO:     Application startup complete.
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:54:34.956541Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:54:35.061590Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:54:35.077054Z"}
WARNING:  WatchFiles detected changes in 'app/api/routes/subscriptions.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/subscriptions.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [59621]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [59622]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [59721]
INFO:     Waiting for application startup.
2025-06-25 06:54:48.295 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:54:48.296 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:54:48.474 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:54:48.547 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:54:48.555 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:54:48.558 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
INFO:     Started server process [59722]
INFO:     Waiting for application startup.
2025-06-25 06:54:48.576 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:54:48.577 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:54:48.675675Z"}
2025-06-25 06:54:48.757 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:54:48.815 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:54:48.816 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:54:48.817 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:54:48.902242Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:54:50.723617Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:54:50.952677Z"}
2025-06-25 06:54:53.743 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:54:53.744 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:54:53.986 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:54:53.988 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:54:54.792001Z"}
INFO:     Application startup complete.
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:54:55.412734Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:54:55.520020Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:54:55.631229Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:54:55.644730Z"}
WARNING:  WatchFiles detected changes in 'app/api/routes/payments/endpoints.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/payments/endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [59722]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [59721]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [59805]
INFO:     Waiting for application startup.
2025-06-25 06:55:04.126 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:55:04.127 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [59804]
INFO:     Waiting for application startup.
2025-06-25 06:55:04.264 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 06:55:04.265 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 06:55:04.302 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 06:55:04.378 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:55:04.379 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:55:04.380 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 06:55:04.434 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:55:04.488866Z"}
2025-06-25 06:55:04.515 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 06:55:04.517 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 06:55:04.518 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:55:04.604760Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:55:06.537766Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:55:06.654173Z"}
2025-06-25 06:55:09.562 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:55:09.563 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 06:55:09.663 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 06:55:09.664 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:55:10.779756Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:55:11.125071Z"}
{"error": "connect() got an unexpected keyword argument 'connect_timeout'", "event": "Database health check failed", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T06:55:11.232970Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T06:55:11.249743Z"}
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [59805]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [59804]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-36:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 115, in <module>
    db: Session = Depends(get_db)
                          ^^^^^^
NameError: name 'get_db' is not defined
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-44:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 115, in <module>
    db: Session = Depends(get_db)
                          ^^^^^^
NameError: name 'get_db' is not defined
WARNING:  WatchFiles detected changes in 'app/api/dependencies.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/dependencies.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-37:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 115, in <module>
    db: Session = Depends(get_db)
                          ^^^^^^
NameError: name 'get_db' is not defined
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-45:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 115, in <module>
    db: Session = Depends(get_db)
                          ^^^^^^
NameError: name 'get_db' is not defined
WARNING:  WatchFiles detected changes in 'app/services/billing_service.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/billing_service.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-38:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 115, in <module>
    db: Session = Depends(get_db)
                          ^^^^^^
NameError: name 'get_db' is not defined
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-46:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 115, in <module>
    db: Session = Depends(get_db)
                          ^^^^^^
NameError: name 'get_db' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-39:
Process SpawnProcess-47:
Traceback (most recent call last):
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 211, in <module>
    db: Session = Depends(get_db)
                          ^^^^^^
NameError: name 'get_db' is not defined
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 211, in <module>
    db: Session = Depends(get_db)
                          ^^^^^^
NameError: name 'get_db' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-48:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 289, in <module>
    db: Session = Depends(get_db)
                          ^^^^^^
NameError: name 'get_db' is not defined
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-40:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 289, in <module>
    db: Session = Depends(get_db)
                          ^^^^^^
NameError: name 'get_db' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-49:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 335, in <module>
    async def process_stripe_payment_succeeded(event: StripeWebhookEvent, db: Session):
                                                                              ^^^^^^^
NameError: name 'Session' is not defined
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-41:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 335, in <module>
    async def process_stripe_payment_succeeded(event: StripeWebhookEvent, db: Session):
                                                                              ^^^^^^^
NameError: name 'Session' is not defined
WARNING:  WatchFiles detected changes in 'app/core/monitoring.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/core/monitoring.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-42:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 335, in <module>
    async def process_stripe_payment_succeeded(event: StripeWebhookEvent, db: Session):
                                                                              ^^^^^^^
NameError: name 'Session' is not defined
Process SpawnProcess-50:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 335, in <module>
    async def process_stripe_payment_succeeded(event: StripeWebhookEvent, db: Session):
                                                                              ^^^^^^^
NameError: name 'Session' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-51:
Process SpawnProcess-43:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 342, in <module>
    async def process_stripe_payment_failed(event: StripeWebhookEvent, db: Session):
                                                                           ^^^^^^^
NameError: name 'Session' is not defined
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 342, in <module>
    async def process_stripe_payment_failed(event: StripeWebhookEvent, db: Session):
                                                                           ^^^^^^^
NameError: name 'Session' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-52:
Process SpawnProcess-44:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 349, in <module>
    async def process_stripe_invoice_paid(event: StripeWebhookEvent, db: Session):
                                                                         ^^^^^^^
NameError: name 'Session' is not defined
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 349, in <module>
    async def process_stripe_invoice_paid(event: StripeWebhookEvent, db: Session):
                                                                         ^^^^^^^
NameError: name 'Session' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-53:
Process SpawnProcess-45:
Traceback (most recent call last):
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 356, in <module>
    async def process_stripe_subscription_created(event: StripeWebhookEvent, db: Session):
                                                                                 ^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 356, in <module>
    async def process_stripe_subscription_created(event: StripeWebhookEvent, db: Session):
                                                                                 ^^^^^^^
NameError: name 'Session' is not defined
NameError: name 'Session' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-46:
Traceback (most recent call last):
Process SpawnProcess-54:
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 363, in <module>
    async def process_stripe_subscription_updated(event: StripeWebhookEvent, db: Session):
                                                                                 ^^^^^^^
NameError: name 'Session' is not defined
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 363, in <module>
    async def process_stripe_subscription_updated(event: StripeWebhookEvent, db: Session):
                                                                                 ^^^^^^^
NameError: name 'Session' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-55:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 370, in <module>
    async def process_stripe_subscription_cancelled(event: StripeWebhookEvent, db: Session):
                                                                                   ^^^^^^^
NameError: name 'Session' is not defined
Process SpawnProcess-47:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 370, in <module>
    async def process_stripe_subscription_cancelled(event: StripeWebhookEvent, db: Session):
                                                                                   ^^^^^^^
NameError: name 'Session' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-56:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 377, in <module>
    async def process_paypal_payment_completed(event: PayPalWebhookEvent, db: Session):
                                                                              ^^^^^^^
NameError: name 'Session' is not defined
Process SpawnProcess-48:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 377, in <module>
    async def process_paypal_payment_completed(event: PayPalWebhookEvent, db: Session):
                                                                              ^^^^^^^
NameError: name 'Session' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-49:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 383, in <module>
    async def process_paypal_payment_denied(event: PayPalWebhookEvent, db: Session):
                                                                           ^^^^^^^
NameError: name 'Session' is not defined
Process SpawnProcess-57:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 383, in <module>
    async def process_paypal_payment_denied(event: PayPalWebhookEvent, db: Session):
                                                                           ^^^^^^^
NameError: name 'Session' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-50:
Process SpawnProcess-58:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 389, in <module>
    async def process_paypal_subscription_created(event: PayPalWebhookEvent, db: Session):
                                                                                 ^^^^^^^
NameError: name 'Session' is not defined
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 389, in <module>
    async def process_paypal_subscription_created(event: PayPalWebhookEvent, db: Session):
                                                                                 ^^^^^^^
NameError: name 'Session' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-59:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 395, in <module>
    async def process_paypal_subscription_cancelled(event: PayPalWebhookEvent, db: Session):
                                                                                   ^^^^^^^
NameError: name 'Session' is not defined
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-51:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 395, in <module>
    async def process_paypal_subscription_cancelled(event: PayPalWebhookEvent, db: Session):
                                                                                   ^^^^^^^
NameError: name 'Session' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-60:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 401, in <module>
    async def process_mpesa_payment_success(event: MPesaWebhookEvent, db: Session):
                                                                          ^^^^^^^
NameError: name 'Session' is not defined
Process SpawnProcess-52:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 401, in <module>
    async def process_mpesa_payment_success(event: MPesaWebhookEvent, db: Session):
                                                                          ^^^^^^^
NameError: name 'Session' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-61:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 409, in <module>
    async def process_mpesa_payment_failure(event: MPesaWebhookEvent, db: Session):
                                                                          ^^^^^^^
NameError: name 'Session' is not defined
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-53:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 16, in <module>
    from .routes.webhooks import router as webhook_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/webhooks.py", line 409, in <module>
    async def process_mpesa_payment_failure(event: MPesaWebhookEvent, db: Session):
                                                                          ^^^^^^^
NameError: name 'Session' is not defined
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/routes/webhooks.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
Process SpawnProcess-62:
Process SpawnProcess-54:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 35, in <module>
    from app.core.monitoring import get_metrics, get_health_check, update_system_metrics_task
ModuleNotFoundError: No module named 'app.core.monitoring'
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 24, in import_from_string
    raise exc from None
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 35, in <module>
    from app.core.monitoring import get_metrics, get_health_check, update_system_metrics_task
ModuleNotFoundError: No module named 'app.core.monitoring'
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [69867]
INFO:     Waiting for application startup.
2025-06-25 07:17:16.189 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:17:16.190 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [69868]
INFO:     Waiting for application startup.
2025-06-25 07:17:16.294 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:17:16.296 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 07:17:16.493 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 07:17:16.566 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 07:17:16.663 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:17:16.665 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:17:16.666 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 07:17:16.688 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:17:16.689 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:17:16.690 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:17:16.826250Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:17:16.843293Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:17:18.899852Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:17:18.905244Z"}
2025-06-25 07:17:22.041 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:17:22.042 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
2025-06-25 07:17:22.048 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:17:22.049 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
ERROR:    Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/routing.py", line 677, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 130, in lifespan
    monitoring_task = asyncio.create_task(update_system_metrics_task())
                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'update_system_metrics_task' is not defined

ERROR:    Application startup failed. Exiting.
ERROR:    Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/routing.py", line 677, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 130, in lifespan
    monitoring_task = asyncio.create_task(update_system_metrics_task())
                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'update_system_metrics_task' is not defined

ERROR:    Application startup failed. Exiting.
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [69897]
INFO:     Waiting for application startup.
2025-06-25 07:17:32.955 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:17:32.956 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [69896]
INFO:     Waiting for application startup.
2025-06-25 07:17:33.040 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:17:33.041 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 07:17:33.228 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 07:17:33.230 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 07:17:33.292 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:17:33.293 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:17:33.294 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 07:17:33.308 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:17:33.309 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:17:33.310 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:17:33.382518Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:17:33.404739Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:17:35.430355Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:17:35.451826Z"}
2025-06-25 07:17:38.490 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:17:38.492 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
2025-06-25 07:17:38.497 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:17:38.498 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.500438Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.503592Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.514067Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.518780Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.522854Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.528742Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.535286Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.541836Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.548406Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.553229Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.560666Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.568091Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.575815Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.582130Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.592649Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.598940Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.604149Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.610705Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.615954Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.622921Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.630340Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.635319Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.640736Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.648343Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.653273Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.658883Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.665808Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.670983Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.676691Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.684003Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.689300Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.695660Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.702456Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.708419Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.715167Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.721614Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.727952Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.733310Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.739793Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.746915Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.752485Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.760054Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:38.764709Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:17:39.480172Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:17:39.500400Z"}
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Waiting for application shutdown.
ERROR:    Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/routing.py", line 677, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/contextlib.py", line 217, in __aexit__
    await anext(self.gen)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 141, in lifespan
    # monitoring_task.cancel()
    ^^^^^^^^^^^^^^^
NameError: name 'monitoring_task' is not defined

ERROR:    Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/starlette/routing.py", line 677, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/contextlib.py", line 217, in __aexit__
    await anext(self.gen)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 141, in lifespan
    # monitoring_task.cancel()
    ^^^^^^^^^^^^^^^
NameError: name 'monitoring_task' is not defined

ERROR:    Application shutdown failed. Exiting.
ERROR:    Application shutdown failed. Exiting.
INFO:     Finished server process [69897]
INFO:     Finished server process [69896]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [69977]
INFO:     Waiting for application startup.
2025-06-25 07:17:47.353 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:17:47.354 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [69976]
INFO:     Waiting for application startup.
2025-06-25 07:17:47.377 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:17:47.378 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 07:17:47.618 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 07:17:47.619 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 07:17:47.703 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:17:47.703 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:17:47.704 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:17:47.705 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 07:17:47.705 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:17:47.706 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:17:47.791422Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:17:47.803683Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:17:49.839098Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:17:49.854156Z"}
2025-06-25 07:17:52.888 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:17:52.890 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
2025-06-25 07:17:52.892 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:17:52.893 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:52.897530Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:17:52.901191Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:17:53.888080Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:17:53.901597Z"}
WARNING:  WatchFiles detected changes in 'app/core/database.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/core/database.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [69977]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [69976]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [70001]
INFO:     Waiting for application startup.
2025-06-25 07:18:01.418 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:18:01.419 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [70002]
INFO:     Waiting for application startup.
2025-06-25 07:18:01.513 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:18:01.514 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 07:18:01.656 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 07:18:01.743 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:18:01.744 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:18:01.745 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 07:18:01.755 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 07:18:01.994 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:18:01.999 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:18:02.001 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:02.054884Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:02.252417Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:04.157427Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:04.315411Z"}
2025-06-25 07:18:07.190 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:18:07.191 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:18:07.197302Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:18:07.199629Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:18:07.206194Z"}
INFO:     127.0.0.1:46614 - "GET /health HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:48828 - "GET /health HTTP/1.1" 503 Service Unavailable
2025-06-25 07:18:07.228 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:18:07.229 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:08.225956Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:08.393294Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:16.274392Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:16.441920Z"}
WARNING:  WatchFiles detected changes in 'app/core/database.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/core/database.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [70002]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [70001]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [70075]
INFO:     Waiting for application startup.
2025-06-25 07:18:24.817 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:18:24.817 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [70076]
INFO:     Waiting for application startup.
2025-06-25 07:18:24.833 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:18:24.833 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 07:18:24.996 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 07:18:25.019 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 07:18:25.081 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:18:25.082 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:18:25.083 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 07:18:25.103 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:18:25.105 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:18:25.107 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:25.177785Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:25.202600Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:27.231234Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:27.254982Z"}
2025-06-25 07:18:30.276 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:18:30.277 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
2025-06-25 07:18:30.284 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:18:30.286 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:18:30.294968Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:18:30.298108Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:31.297187Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:31.320439Z"}
WARNING:  WatchFiles detected changes in 'app/core/database.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/core/database.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [70075]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [70076]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [70109]
INFO:     Waiting for application startup.
2025-06-25 07:18:38.249 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:18:38.250 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [70108]
INFO:     Waiting for application startup.
2025-06-25 07:18:38.362 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:18:38.363 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 07:18:38.422 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 07:18:38.489 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:18:38.490 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:18:38.490 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 07:18:38.538 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:38.590159Z"}
2025-06-25 07:18:38.602 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:18:38.603 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:18:38.604 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:38.687784Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:40.638703Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:40.739286Z"}
2025-06-25 07:18:43.651 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:18:43.652 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:18:43.660608Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:18:43.663270Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:18:43.669708Z"}
INFO:     127.0.0.1:43430 - "GET /health HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:43438 - "GET /health HTTP/1.1" 503 Service Unavailable
2025-06-25 07:18:43.695 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:18:43.697 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:44.685649Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:44.785709Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:52.735714Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:18:52.848458Z"}
WARNING:  WatchFiles detected changes in 'app/core/database.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/core/database.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [70109]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [70108]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [70185]
INFO:     Waiting for application startup.
2025-06-25 07:19:02.102 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:19:02.103 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [70186]
INFO:     Waiting for application startup.
2025-06-25 07:19:02.233 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:19:02.234 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 07:19:02.315 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 07:19:02.436 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:19:02.437 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:19:02.438 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 07:19:02.459 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 07:19:02.545 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:19:02.547 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:19:02.547 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:19:02.556621Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:19:02.656264Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:19:04.621585Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:19:04.729076Z"}
2025-06-25 07:19:07.615 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:19:07.617 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:19:07.624141Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:19:07.626279Z"}
INFO:     127.0.0.1:38048 - "GET /health HTTP/1.1" 503 Service Unavailable
2025-06-25 07:19:07.649 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:19:07.650 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:19:08.674529Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:19:08.795791Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:19:14.048990Z"}
INFO:     127.0.0.1:55460 - "GET /health HTTP/1.1" 503 Service Unavailable
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:19:16.724712Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:19:16.835153Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 5 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:19:32.768393Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 5 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:19:32.873408Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:19:47.603203Z"}
INFO:     127.0.0.1:46566 - "GET /health HTTP/1.1" 503 Service Unavailable
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 6 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:20:04.806770Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 6 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:20:04.913164Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:20:21.300210Z"}
INFO:     127.0.0.1:45692 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:20:54.829470Z"}
INFO:     127.0.0.1:51206 - "GET /health HTTP/1.1" 503 Service Unavailable
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 7 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:21:08.844860Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 7 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:21:08.955065Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:21:28.791080Z"}
INFO:     127.0.0.1:35062 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:22:02.547349Z"}
INFO:     127.0.0.1:46636 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:22:36.291558Z"}
INFO:     127.0.0.1:41122 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:23:09.951314Z"}
INFO:     127.0.0.1:42920 - "GET /health HTTP/1.1" 503 Service Unavailable
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 8 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:23:16.891923Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 8 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:23:17.015048Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:23:43.806144Z"}
INFO:     127.0.0.1:59508 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:24:17.798223Z"}
INFO:     127.0.0.1:56240 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:24:51.544758Z"}
INFO:     127.0.0.1:37994 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:25:25.080020Z"}
INFO:     127.0.0.1:36644 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:25:58.568223Z"}
INFO:     127.0.0.1:43428 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:26:32.331583Z"}
INFO:     127.0.0.1:45470 - "GET /health HTTP/1.1" 503 Service Unavailable
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [70185]
INFO:     Stopping reloader process [42578]
=== 2025-06-25 07:26:59 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7309 (Press CTRL+C to quit)
INFO:     Started reloader process [71970] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [71973]
INFO:     Waiting for application startup.
2025-06-25 07:27:04.069 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:27:04.070 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 07:27:04.309 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 07:27:04.404 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:27:04.405 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:27:04.406 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:27:04.491524Z"}
2025-06-25 07:27:04.572 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:27:04.573 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:27:06.345653Z"}
INFO:     127.0.0.1:53966 - "GET /health HTTP/1.1" 503 Service Unavailable
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:27:06.555093Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:27:10.596041Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:27:18.634229Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 9 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:27:33.080899Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 5 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:27:34.676458Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:27:39.946478Z"}
INFO:     127.0.0.1:52628 - "GET /health HTTP/1.1" 503 Service Unavailable
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 6 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:28:06.715307Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:28:13.800305Z"}
INFO:     127.0.0.1:37998 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:28:47.554005Z"}
INFO:     127.0.0.1:35886 - "GET /health HTTP/1.1" 503 Service Unavailable
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 7 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:29:10.757805Z"}
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:29:21.127587Z"}
INFO:     127.0.0.1:49552 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'get_health_check' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:29:54.563015Z"}
INFO:     127.0.0.1:39254 - "GET /health HTTP/1.1" 503 Service Unavailable
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [70186]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [71973]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [73854]
INFO:     Waiting for application startup.
2025-06-25 07:30:19.615 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:30:19.615 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [73853]
INFO:     Waiting for application startup.
2025-06-25 07:30:19.622 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:30:19.623 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 07:30:19.909 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 07:30:19.912 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 07:30:20.158 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:30:20.160 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:30:20.161 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 07:30:20.177 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:30:20.180 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:30:20.181 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:30:20.327267Z"}
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:30:20.329351Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:30:22.385320Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:30:22.398102Z"}
2025-06-25 07:30:25.517 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:30:25.518 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
2025-06-25 07:30:25.523 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:30:25.524 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:30:25.530182Z"}
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:30:25.532190Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:30:26.443469Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:30:26.450103Z"}
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:30:28.195581Z"}
INFO:     127.0.0.1:41884 - "GET /health HTTP/1.1" 503 Service Unavailable
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [73853]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [73854]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [73919]
INFO:     Waiting for application startup.
2025-06-25 07:30:34.576 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:30:34.578 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 07:30:34.771 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
INFO:     Started server process [73920]
INFO:     Waiting for application startup.
2025-06-25 07:30:34.778 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 07:30:34.779 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 07:30:34.844 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:30:34.846 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:30:34.847 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:30:34.988644Z"}
2025-06-25 07:30:35.010 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 07:30:35.091 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 07:30:35.094 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 07:30:35.095 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:30:35.204515Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:30:37.076996Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:30:37.291738Z"}
2025-06-25 07:30:40.074 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:30:40.075 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
2025-06-25 07:30:40.286 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 07:30:40.287 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:30:40.293334Z"}
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:30:40.295199Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:30:41.135010Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:30:41.346464Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:30:49.174259Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:30:49.395805Z"}
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:31:01.795735Z"}
INFO:     127.0.0.1:48770 - "GET /health HTTP/1.1" 503 Service Unavailable
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 5 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:31:05.215071Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 5 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:31:05.445290Z"}
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:31:35.286317Z"}
INFO:     127.0.0.1:35232 - "GET /health HTTP/1.1" 503 Service Unavailable
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 6 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:31:37.267664Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 6 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:31:37.519167Z"}
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:32:09.056634Z"}
INFO:     127.0.0.1:33462 - "GET /health HTTP/1.1" 503 Service Unavailable
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 7 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:32:41.305203Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 7 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:32:41.567331Z"}
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:32:42.821402Z"}
INFO:     127.0.0.1:35952 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:33:16.554747Z"}
INFO:     127.0.0.1:36644 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:33:50.544272Z"}
INFO:     127.0.0.1:51840 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:34:24.160605Z"}
INFO:     127.0.0.1:46482 - "GET /health HTTP/1.1" 503 Service Unavailable
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 8 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:34:49.369209Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 8 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:34:49.641262Z"}
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:34:58.319103Z"}
INFO:     127.0.0.1:54206 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:35:31.886211Z"}
INFO:     127.0.0.1:37792 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:36:05.559717Z"}
INFO:     127.0.0.1:54504 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:36:39.297366Z"}
INFO:     127.0.0.1:46146 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:37:12.800880Z"}
INFO:     127.0.0.1:49300 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:37:46.347930Z"}
INFO:     127.0.0.1:44722 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:38:20.320433Z"}
INFO:     127.0.0.1:35524 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:38:53.878140Z"}
INFO:     127.0.0.1:50498 - "GET /health HTTP/1.1" 503 Service Unavailable
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 9 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:39:05.442544Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 9 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:39:05.707593Z"}
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:39:27.409086Z"}
INFO:     127.0.0.1:55168 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:40:00.813634Z"}
INFO:     127.0.0.1:59142 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:40:34.347156Z"}
INFO:     127.0.0.1:60382 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:41:08.169148Z"}
INFO:     127.0.0.1:35292 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:41:42.063286Z"}
INFO:     127.0.0.1:49868 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:42:15.547153Z"}
INFO:     127.0.0.1:49310 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:42:49.558681Z"}
INFO:     127.0.0.1:48346 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:43:23.296107Z"}
INFO:     127.0.0.1:42028 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:43:57.043922Z"}
INFO:     127.0.0.1:49072 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:44:30.819415Z"}
INFO:     127.0.0.1:35562 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:45:04.641197Z"}
INFO:     127.0.0.1:46898 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:45:38.547729Z"}
INFO:     127.0.0.1:47844 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:46:12.302214Z"}
INFO:     127.0.0.1:56760 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:46:45.892650Z"}
INFO:     127.0.0.1:33360 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:47:20.068134Z"}
INFO:     127.0.0.1:59368 - "GET /health HTTP/1.1" 503 Service Unavailable
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 10 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:47:37.522084Z"}
{"event": "\u274c All database initialization attempts failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:47:37.522562Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 10 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T07:47:37.798804Z"}
{"event": "\u274c All database initialization attempts failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:47:37.799286Z"}
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:47:53.675853Z"}
INFO:     127.0.0.1:54948 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:48:27.562864Z"}
INFO:     127.0.0.1:39562 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:49:01.199783Z"}
INFO:     127.0.0.1:49616 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:49:35.309591Z"}
INFO:     127.0.0.1:50290 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:50:09.328546Z"}
INFO:     127.0.0.1:56444 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:50:42.833014Z"}
INFO:     127.0.0.1:57726 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:51:16.793327Z"}
INFO:     127.0.0.1:35452 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:51:50.546402Z"}
INFO:     127.0.0.1:58604 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:52:23.826564Z"}
INFO:     127.0.0.1:53584 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:52:57.555741Z"}
INFO:     127.0.0.1:60716 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:53:31.301238Z"}
INFO:     127.0.0.1:48860 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:54:05.093637Z"}
INFO:     127.0.0.1:54316 - "GET /health HTTP/1.1" 503 Service Unavailable
{"error": "name 'datetime' is not defined", "event": "Health check failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T07:54:38.676621Z"}
INFO:     127.0.0.1:41974 - "GET /health HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:58786 - "GET /health HTTP/1.1" 200 OK
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 8 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:01:44.159056Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 8 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:01:44.192435Z"}
INFO:     127.0.0.1:59338 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:48012 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:56166 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:36078 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:50774 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:59558 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:57448 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:59020 - "GET /health HTTP/1.1" 200 OK
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 9 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:06:00.210312Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 9 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:06:00.235304Z"}
INFO:     127.0.0.1:50762 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:34102 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:37056 - "GET /health HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [79740]
INFO:     Stopping reloader process [71970]
=== 2025-06-25 08:07:51 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7309 (Press CTRL+C to quit)
INFO:     Started reloader process [81968] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [81973]
INFO:     Waiting for application startup.
2025-06-25 08:07:55.616 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 08:07:55.617 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 08:07:55.832 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 08:07:55.930 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 08:07:55.932 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 08:07:55.932 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:07:56.040903Z"}
2025-06-25 08:07:56.264 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 08:07:56.266 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:07:58.129105Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:08:02.225144Z"}
INFO:     127.0.0.1:38692 - "GET /health HTTP/1.1" 200 OK
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:08:10.281100Z"}
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 5 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:08:26.337136Z"}
INFO:     127.0.0.1:55902 - "GET /health HTTP/1.1" 200 OK
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 6 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:08:58.375984Z"}
INFO:     127.0.0.1:57498 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:56456 - "GET /health HTTP/1.1" 200 OK
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 7 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:10:02.427115Z"}
INFO:     127.0.0.1:56378 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:54630 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:57402 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:48972 - "GET /health HTTP/1.1" 200 OK
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 8 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:12:10.491993Z"}
INFO:     127.0.0.1:44590 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:47262 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:57254 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:60680 - "GET /health HTTP/1.1" 200 OK
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 10 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:14:32.291199Z"}
{"event": "\u274c All database initialization attempts failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T08:14:32.291574Z"}
INFO:     127.0.0.1:41904 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:46570 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:41610 - "GET /health HTTP/1.1" 200 OK
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 9 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:16:26.552793Z"}
INFO:     127.0.0.1:54122 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:60360 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39384 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:33984 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:57998 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:56694 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:33738 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:32896 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:60424 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:51546 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:57900 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:34588 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:60410 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:34560 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:54208 - "GET /health HTTP/1.1" 200 OK
Error calling PostgreSQL Cluster Service: All connection attempts failed
❌ PostgreSQL Cluster Service is not available
{"event": "\u26a0\ufe0f Database initialization attempt 10 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:24:58.658449Z"}
{"event": "\u274c All database initialization attempts failed", "logger": "main", "level": "error", "timestamp": "2025-06-25T08:24:58.665022Z"}
INFO:     127.0.0.1:41108 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:59570 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39592 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:35380 - "GET /health HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/core/config.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/core/config.py'. Reloading...
INFO:     Shutting down
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [79739]
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [81973]
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [85574]
INFO:     Waiting for application startup.
2025-06-25 08:26:52.838 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 08:26:52.839 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
INFO:     Started server process [85573]
INFO:     Waiting for application startup.
2025-06-25 08:26:52.848 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for billing
2025-06-25 08:26:52.849 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for billing
2025-06-25 08:26:53.127 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 08:26:53.134 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for billing
2025-06-25 08:26:53.299 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 08:26:53.301 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for billing
2025-06-25 08:26:53.301 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 08:26:53.302 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
2025-06-25 08:26:53.303 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for billing
2025-06-25 08:26:53.303 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for billing
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
❌ Failed to create subscription plan: Free
❌ Failed to create subscription plan: Free
❌ Failed to create default subscription plans
❌ Failed to create default subscription plans
❌ Failed to initialize billing data
❌ Failed to initialize billing data
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:26:54.138745Z"}
{"event": "\u26a0\ufe0f Database initialization attempt 1 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:26:54.139005Z"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
❌ Failed to create subscription plan: Free
❌ Failed to create default subscription plans
❌ Failed to initialize billing data
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:26:56.336602Z"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
❌ Failed to create subscription plan: Free
❌ Failed to create default subscription plans
❌ Failed to initialize billing data
{"event": "\u26a0\ufe0f Database initialization attempt 2 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:26:56.340992Z"}
2025-06-25 08:26:58.655 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 08:26:58.656 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
2025-06-25 08:26:58.663 | INFO     | app.services.inter_service_client:_register_billing_service:91 - ✅ Billing service registered with service registry
2025-06-25 08:26:58.664 | INFO     | app.services.inter_service_client:initialize:38 - ✅ Billing service client initialized
INFO:     Application startup complete.
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
❌ Failed to create subscription plan: Free
❌ Failed to create default subscription plans
❌ Failed to initialize billing data
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:27:00.513311Z"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
❌ Failed to create subscription plan: Free
❌ Failed to create default subscription plans
❌ Failed to initialize billing data
{"event": "\u26a0\ufe0f Database initialization attempt 3 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:27:00.523742Z"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
❌ Failed to create subscription plan: Free
❌ Failed to create default subscription plans
❌ Failed to initialize billing data
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:27:08.800879Z"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
❌ Failed to create subscription plan: Free
❌ Failed to create default subscription plans
❌ Failed to initialize billing data
{"event": "\u26a0\ufe0f Database initialization attempt 4 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:27:08.854651Z"}
INFO:     127.0.0.1:58414 - "GET /health HTTP/1.1" 200 OK
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
❌ Failed to create subscription plan: Free
❌ Failed to create default subscription plans
❌ Failed to initialize billing data
{"event": "\u26a0\ufe0f Database initialization attempt 5 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:27:24.984383Z"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
❌ Failed to create subscription plan: Free
❌ Failed to create default subscription plans
❌ Failed to initialize billing data
{"event": "\u26a0\ufe0f Database initialization attempt 5 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:27:25.028192Z"}
INFO:     127.0.0.1:56320 - "GET /health HTTP/1.1" 200 OK
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
❌ Failed to create subscription plan: Free
❌ Failed to create default subscription plans
❌ Failed to initialize billing data
{"event": "\u26a0\ufe0f Database initialization attempt 6 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:27:57.226575Z"}
HTTP error calling PostgreSQL Cluster Service: 401 - {"detail":"Invalid service token"}
❌ Failed to create subscription plan: Free
❌ Failed to create default subscription plans
❌ Failed to initialize billing data
{"event": "\u26a0\ufe0f Database initialization attempt 6 failed", "logger": "main", "level": "warning", "timestamp": "2025-06-25T08:27:57.238166Z"}
