=== 2025-06-25 04:04:36 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [10844] using WatchFiles
INFO:     Started server process [10847]
INFO:     Waiting for application startup.
2025-06-25 04:04:41.687 | INFO     | main:startup_event:530 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-25 04:04:41.688 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-25 04:04:41.689 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-25 04:04:42.041 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-25 04:04:42.143 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-25 04:04:42.145 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-25 04:04:42.146 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-25 04:04:43.238 | INFO     | main:startup_event:555 - 🔧 Starting database initialization...
2025-06-25 04:04:46.208 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-25 04:04:46.210 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-25 04:04:46.330 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-25 04:04:46.333 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-25 04:04:46.764 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-25 04:04:46.767 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-25 04:04:46.773 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-25 04:04:47.638 | INFO     | app.services.database.init_database:_insert_default_permissions:226 - ➕ Added permission: read
2025-06-25 04:04:47.662 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (fd1e2540-3d2a-4cb5-9250-9fbf3a97d38e, read, Read access, null, null, 2025-06-25 04:04:47.654841, 2025-06-25 04:04:47.654855).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('fd1e2540-3d2a-4cb5-9250-9fbf3a97d38e'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 25, 4, 4, 47, 654841), 'updated_at': datetime.datetime(2025, 6, 25, 4, 4, 47, 654855)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-25 04:04:47.665 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (fd1e2540-3d2a-4cb5-9250-9fbf3a97d38e, read, Read access, null, null, 2025-06-25 04:04:47.654841, 2025-06-25 04:04:47.654855).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('fd1e2540-3d2a-4cb5-9250-9fbf3a97d38e'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 25, 4, 4, 47, 654841), 'updated_at': datetime.datetime(2025, 6, 25, 4, 4, 47, 654855)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-25 04:04:47.667 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (fd1e2540-3d2a-4cb5-9250-9fbf3a97d38e, read, Read access, null, null, 2025-06-25 04:04:47.654841, 2025-06-25 04:04:47.654855).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('fd1e2540-3d2a-4cb5-9250-9fbf3a97d38e'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 25, 4, 4, 47, 654841), 'updated_at': datetime.datetime(2025, 6, 25, 4, 4, 47, 654855)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-25 04:04:47.667 | WARNING  | main:startup_event:565 - ⚠️ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (fd1e2540-3d2a-4cb5-9250-9fbf3a97d38e, read, Read access, null, null, 2025-06-25 04:04:47.654841, 2025-06-25 04:04:47.654855).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('fd1e2540-3d2a-4cb5-9250-9fbf3a97d38e'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 25, 4, 4, 47, 654841), 'updated_at': datetime.datetime(2025, 6, 25, 4, 4, 47, 654855)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-25 04:04:47.668 | INFO     | main:startup_event:566 - 🔄 Service will continue in basic mode...
2025-06-25 04:04:47.669 | INFO     | main:startup_event:575 - ✅ PostgreSQL connection pool initialized successfully
2025-06-25 04:04:47.670 | INFO     | main:startup_event:576 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:60674 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60880 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35792 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36258 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47606 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35586 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51804 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60126 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44106 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44120 - "GET /health HTTP/1.1" 200 OK
2025-06-25 04:09:12.906 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:44128 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:50420 - "GET /metrics HTTP/1.1" 404 Not Found
2025-06-25 04:09:59.461 | INFO     | main:select_records:347 - 📥 Selected 0 records from users
INFO:     127.0.0.1:55394 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-25 04:10:00.722 | INFO     | main:create_user:405 - 👤 Created user: testuser991
INFO:     127.0.0.1:55400 - "POST /api/v1/users/create HTTP/1.1" 200 OK
INFO:     127.0.0.1:59778 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50480 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39902 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48462 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57760 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59616 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49964 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43286 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36180 - "GET /metrics HTTP/1.1" 404 Not Found
2025-06-25 04:14:19.536 | INFO     | main:authenticate_user:462 - ✅ User found for authentication: <EMAIL>
INFO:     127.0.0.1:37104 - "POST /api/v1/users/authenticate HTTP/1.1" 200 OK
2025-06-25 04:14:20.857 | INFO     | main:update_user_login:507 - ✅ Updated login info <NAME_EMAIL>
INFO:     127.0.0.1:37114 - "POST /api/v1/users/update-login?user_id=6983824c-9fa5-40bc-9d45-1d7c6cf7c70b&success=False HTTP/1.1" 200 OK
INFO:     127.0.0.1:43524 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50984 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41414 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59340 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51486 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47252 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58776 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58340 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52448 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50722 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60332 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59498 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53412 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51222 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45828 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41672 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46214 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57636 - "GET /metrics HTTP/1.1" 404 Not Found
2025-06-25 04:23:12.183 | INFO     | main:authenticate_user:462 - ✅ User found for authentication: <EMAIL>
INFO:     127.0.0.1:57642 - "POST /api/v1/users/authenticate HTTP/1.1" 200 OK
2025-06-25 04:23:13.322 | INFO     | main:update_user_login:507 - ✅ Updated login info <NAME_EMAIL>
INFO:     127.0.0.1:57658 - "POST /api/v1/users/update-login?user_id=6983824c-9fa5-40bc-9d45-1d7c6cf7c70b&success=True HTTP/1.1" 200 OK
INFO:     127.0.0.1:51986 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58934 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51858 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53668 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54888 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56006 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35562 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57442 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40970 - "GET /metrics HTTP/1.1" 404 Not Found
2025-06-25 04:27:57.097 | INFO     | main:authenticate_user:462 - ✅ User found for authentication: <EMAIL>
INFO:     127.0.0.1:47042 - "POST /api/v1/users/authenticate HTTP/1.1" 200 OK
2025-06-25 04:27:58.133 | INFO     | main:update_user_login:507 - ✅ Updated login info <NAME_EMAIL>
INFO:     127.0.0.1:47054 - "POST /api/v1/users/update-login?user_id=6983824c-9fa5-40bc-9d45-1d7c6cf7c70b&success=True HTTP/1.1" 200 OK
INFO:     127.0.0.1:45904 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53174 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56472 - "GET /api/v1/users/6983824c-9fa5-40bc-9d45-1d7c6cf7c70b HTTP/1.1" 404 Not Found
2025-06-25 04:28:47.272 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:56476 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-25 04:28:47.346 | ERROR    | main:insert_record:314 - ❌ Failed to insert record into token_blacklist: invalid input for query argument $3: '2025-06-25T04:57:58' (expected a datetime.date or datetime.datetime instance, got 'str')
INFO:     127.0.0.1:56478 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:37716 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47270 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35314 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38024 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50556 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38984 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32998 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42150 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56234 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48006 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44348 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60924 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42684 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52172 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35008 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54518 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55106 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33088 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40630 - "GET /metrics HTTP/1.1" 404 Not Found
2025-06-25 04:38:26.778 | INFO     | main:authenticate_user:462 - ✅ User found for authentication: <EMAIL>
INFO:     127.0.0.1:55034 - "POST /api/v1/users/authenticate HTTP/1.1" 200 OK
2025-06-25 04:38:27.868 | INFO     | main:update_user_login:507 - ✅ Updated login info <NAME_EMAIL>
INFO:     127.0.0.1:55036 - "POST /api/v1/users/update-login?user_id=6983824c-9fa5-40bc-9d45-1d7c6cf7c70b&success=True HTTP/1.1" 200 OK
INFO:     127.0.0.1:49904 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39482 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34740 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48682 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35556 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60932 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51770 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42714 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51066 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42262 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54926 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49424 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59936 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38182 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60334 - "GET /health HTTP/1.1" 200 OK
2025-06-25 04:45:24.228 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:60344 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:48968 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41686 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50018 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40302 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36382 - "GET /api/v1/users/6983824c-9fa5-40bc-9d45-1d7c6cf7c70b HTTP/1.1" 404 Not Found
2025-06-25 04:47:20.287 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:36390 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:36392 - "PUT /api/v1/records/update HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40466 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40286 - "GET /api/v1/users/6983824c-9fa5-40bc-9d45-1d7c6cf7c70b HTTP/1.1" 404 Not Found
2025-06-25 04:48:08.956 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:40302 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:40308 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45092 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39528 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:55596 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55606 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:56894 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:37444 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43124 - "GET /health HTTP/1.1" 200 OK
2025-06-25 04:49:51.540 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:43134 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:47368 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33766 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56202 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39318 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51508 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52846 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49908 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48080 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50526 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52380 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51580 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33232 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51382 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44904 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57420 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58532 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52150 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44814 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60032 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57682 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33342 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34002 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42572 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37350 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40390 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55370 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37708 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50796 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58360 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35260 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58536 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39558 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37940 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54204 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58018 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60990 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51970 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53950 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56430 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36006 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44412 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49924 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43048 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39402 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34832 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32946 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39116 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42538 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41306 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39476 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34530 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60498 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43790 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40484 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45386 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47726 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46370 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33642 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51250 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33854 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41588 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49902 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57752 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58588 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48128 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33316 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53880 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41620 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43652 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40118 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57324 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40012 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42174 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44384 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37488 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57634 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46628 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47838 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41882 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50336 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60934 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58936 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43098 - "GET /metrics HTTP/1.1" 404 Not Found
2025-06-25 06:07:20.228 | INFO     | main:authenticate_user:462 - ✅ User found for authentication: <EMAIL>
INFO:     127.0.0.1:57596 - "POST /api/v1/users/authenticate HTTP/1.1" 200 OK
INFO:     127.0.0.1:57598 - "GET /metrics HTTP/1.1" 404 Not Found
2025-06-25 06:07:21.581 | INFO     | main:update_user_login:507 - ✅ Updated login info <NAME_EMAIL>
INFO:     127.0.0.1:57608 - "POST /api/v1/users/update-login?user_id=5ab59205-a551-4c58-b641-a4ea727a8905&success=False HTTP/1.1" 200 OK
INFO:     127.0.0.1:35556 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47398 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37182 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45322 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40398 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38678 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49172 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44742 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37910 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50102 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59346 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53810 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59156 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49296 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38532 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45540 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59494 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40848 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40140 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52470 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55830 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46536 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38674 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55836 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51824 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49474 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45086 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40168 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44928 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41994 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59650 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45446 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39238 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60370 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57668 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39020 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51134 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34080 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46740 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32778 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49562 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59538 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44014 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51336 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55996 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51244 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52540 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53174 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38002 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57410 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39250 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55386 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59194 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49374 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56322 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59796 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44224 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47754 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48020 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42962 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50198 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38538 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59276 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39584 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49212 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47008 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55934 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48790 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59568 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51334 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51510 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37122 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59294 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35438 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56162 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44790 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57324 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44988 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50960 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44256 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38222 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45498 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35724 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45674 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43876 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60274 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59696 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49056 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40898 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36880 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50072 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50146 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44166 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39296 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59194 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60892 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56298 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43666 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51722 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52736 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60728 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52408 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45048 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55730 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46496 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57358 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53940 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40432 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55668 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56750 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41074 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51184 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52730 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40766 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46646 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58602 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34490 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42936 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55902 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57844 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49376 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36444 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54368 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42340 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46246 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35704 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55938 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46464 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33606 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45170 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59840 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34706 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49546 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51660 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53124 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46332 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58662 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59354 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40128 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53780 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53794 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43556 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37708 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49864 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48802 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35518 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37034 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34472 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51272 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56038 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48346 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42664 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46938 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49792 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59928 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38760 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44386 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48368 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50440 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41864 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44562 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39890 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51366 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49180 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35388 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43722 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55712 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59416 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44322 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41230 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51170 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49816 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38984 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43084 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48386 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48014 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57304 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59460 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38860 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49962 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34764 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58570 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47942 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46328 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54656 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51590 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59938 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37454 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39566 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36854 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36030 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45472 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57788 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59542 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40160 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39304 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54042 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51272 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58482 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35266 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33646 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55622 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48452 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56732 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35992 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35506 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53188 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48666 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57650 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36714 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60626 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42230 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51094 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45970 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55636 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58380 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51490 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33206 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36438 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48058 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33928 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54702 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60252 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41314 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53926 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53328 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42198 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41576 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34442 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60846 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48666 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54984 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39438 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47184 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45976 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50798 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45608 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37164 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48154 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35566 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45222 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42656 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55126 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39794 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37326 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54132 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35432 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59810 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51966 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60438 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36592 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56116 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34044 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50992 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57852 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32868 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34394 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56470 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51364 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54546 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43092 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44940 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33586 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37268 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46714 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54582 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37012 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54930 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47610 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50294 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32914 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48196 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59970 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35116 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37178 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38488 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60470 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33172 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56298 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56322 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:56306 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:46778 - "POST /api/v1/records/select HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:46782 - "POST /api/v1/records/select HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:46792 - "POST /api/v1/records/insert HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:46808 - "POST /api/v1/records/insert HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:46820 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:46822 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:46840 - "POST /api/v1/records/select HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:46826 - "POST /api/v1/records/select HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:46856 - "POST /api/v1/records/insert HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:46868 - "POST /api/v1/records/insert HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:46878 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:46894 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:46904 - "POST /api/v1/records/select HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:46908 - "POST /api/v1/records/select HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:46918 - "POST /api/v1/records/insert HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:46920 - "POST /api/v1/records/insert HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:43602 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:43608 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:43624 - "POST /api/v1/records/select HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:43634 - "POST /api/v1/records/select HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:43650 - "POST /api/v1/records/insert HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:43652 - "POST /api/v1/records/insert HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:57424 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46246 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:46248 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:46264 - "POST /api/v1/records/select HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:46266 - "POST /api/v1/records/select HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:46280 - "POST /api/v1/records/insert HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:46286 - "POST /api/v1/records/insert HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:43004 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39532 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39540 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39546 - "POST /api/v1/records/select HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:39552 - "POST /api/v1/records/select HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:39564 - "POST /api/v1/records/insert HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:39574 - "POST /api/v1/records/insert HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:33386 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55944 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39474 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39486 - "GET /health HTTP/1.1" 200 OK
2025-06-25 08:29:04.965 | INFO     | main:select_records:347 - 📥 Selected 0 records from subscription_plans
INFO:     127.0.0.1:39506 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-25 08:29:04.973 | INFO     | main:select_records:347 - 📥 Selected 0 records from subscription_plans
INFO:     127.0.0.1:39496 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-25 08:29:05.042 | ERROR    | main:insert_record:314 - ❌ Failed to insert record into subscription_plans: column "features" of relation "subscription_plans" does not exist
INFO:     127.0.0.1:39522 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
2025-06-25 08:29:05.045 | ERROR    | main:insert_record:314 - ❌ Failed to insert record into subscription_plans: column "features" of relation "subscription_plans" does not exist
INFO:     127.0.0.1:39534 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:39548 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39556 - "GET /health HTTP/1.1" 200 OK
2025-06-25 08:29:07.234 | INFO     | main:select_records:347 - 📥 Selected 0 records from subscription_plans
INFO:     127.0.0.1:39558 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-25 08:29:07.239 | INFO     | main:select_records:347 - 📥 Selected 0 records from subscription_plans
INFO:     127.0.0.1:39560 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-25 08:29:07.329 | ERROR    | main:insert_record:314 - ❌ Failed to insert record into subscription_plans: column "features" of relation "subscription_plans" does not exist
INFO:     127.0.0.1:39576 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
2025-06-25 08:29:07.349 | ERROR    | main:insert_record:314 - ❌ Failed to insert record into subscription_plans: column "features" of relation "subscription_plans" does not exist
INFO:     127.0.0.1:39580 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:39592 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39594 - "GET /health HTTP/1.1" 200 OK
2025-06-25 08:29:11.516 | INFO     | main:select_records:347 - 📥 Selected 0 records from subscription_plans
INFO:     127.0.0.1:39610 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-25 08:29:11.523 | INFO     | main:select_records:347 - 📥 Selected 0 records from subscription_plans
INFO:     127.0.0.1:39614 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-25 08:29:11.629 | ERROR    | main:insert_record:314 - ❌ Failed to insert record into subscription_plans: column "features" of relation "subscription_plans" does not exist
INFO:     127.0.0.1:39630 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
2025-06-25 08:29:11.633 | ERROR    | main:insert_record:314 - ❌ Failed to insert record into subscription_plans: column "features" of relation "subscription_plans" does not exist
INFO:     127.0.0.1:39626 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:55766 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:55776 - "GET /health HTTP/1.1" 200 OK
2025-06-25 08:29:19.802 | INFO     | main:select_records:347 - 📥 Selected 0 records from subscription_plans
INFO:     127.0.0.1:55782 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-25 08:29:19.826 | INFO     | main:select_records:347 - 📥 Selected 0 records from subscription_plans
INFO:     127.0.0.1:55792 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-25 08:29:19.948 | ERROR    | main:insert_record:314 - ❌ Failed to insert record into subscription_plans: column "features" of relation "subscription_plans" does not exist
INFO:     127.0.0.1:55796 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
2025-06-25 08:29:19.960 | ERROR    | main:insert_record:314 - ❌ Failed to insert record into subscription_plans: column "features" of relation "subscription_plans" does not exist
INFO:     127.0.0.1:55798 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:55812 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50062 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:50078 - "GET /health HTTP/1.1" 200 OK
2025-06-25 08:29:36.102 | INFO     | main:select_records:347 - 📥 Selected 0 records from subscription_plans
INFO:     127.0.0.1:50084 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-25 08:29:36.104 | INFO     | main:select_records:347 - 📥 Selected 0 records from subscription_plans
INFO:     127.0.0.1:50096 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-25 08:29:36.171 | ERROR    | main:insert_record:314 - ❌ Failed to insert record into subscription_plans: column "features" of relation "subscription_plans" does not exist
INFO:     127.0.0.1:50102 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
2025-06-25 08:29:36.174 | ERROR    | main:insert_record:314 - ❌ Failed to insert record into subscription_plans: column "features" of relation "subscription_plans" does not exist
INFO:     127.0.0.1:50110 - "POST /api/v1/records/insert HTTP/1.1" 500 Internal Server Error
