=== 2025-06-25 04:04:12 - Starting vault-consul ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/vault-consul']
INFO:     Uvicorn running on http://0.0.0.0:7200 (Press CTRL+C to quit)
INFO:     Started reloader process [10720] using WatchFiles
INFO:     Started server process [10723]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Vault-Consul Service...
INFO:app.vault_client:🔐 Initializing Vault client for vault-consul
INFO:app.vault_client:🔧 Vault URL: http://localhost:8200
INFO:app.vault_client:🔧 Auth method: token
INFO:app.vault_client:🔧 Environment: development
INFO:app.vault_client:🔍 Checking Vault connectivity...
WARNING:app.vault_client:⚠️ Cannot connect to Vault server: Cannot connect to host localhost:8200 ssl:default [Multiple exceptions: [Errno 111] Connect call failed ('::1', 8200, 0, 0), [<PERSON>rrno 111] Connect call failed ('127.0.0.1', 8200)]
WARNING:app.vault_client:⚠️ Vault server is not reachable - switching to fallback mode
INFO:app.vault_client:🔄 Initializing in fallback mode...
INFO:app.vault_client:💡 Fallback mode uses:
INFO:app.vault_client:   - Environment variables from your system
INFO:app.vault_client:   - Default configuration values
INFO:app.vault_client:   - Local .env file values (if available)
INFO:app.vault_client:💡 To enable Vault integration:
INFO:app.vault_client:   - Ensure Vault server is running and accessible
INFO:app.vault_client:   - Set VAULT_TOKEN environment variable for token auth
INFO:app.vault_client:   - Or deploy in Kubernetes for service account auth
INFO:app.vault_client:   - Check Vault URL configuration
INFO:app.vault_client:🔧 Current Vault URL: http://localhost:8200
INFO:app.vault_client:🔧 Current Auth Method: token
INFO:app.vault_client:🔧 VAULT_TOKEN is set
INFO:main:✅ Vault client initialized successfully
INFO:main:📋 Consul integration ready
INFO:main:🎯 Vault-Consul Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:46414 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 04:04:32.129 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: mongodb-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for mongodb-cluster
INFO:app.config_manager:✅ Configuration manager initialized for mongodb-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 04:04:32.178 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for mongodb-cluster
INFO:     127.0.0.1:46418 - "GET /api/v1/secrets/mongodb-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:56984 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56994 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 04:04:42.115 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: postgresql-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for postgresql-cluster
INFO:app.config_manager:✅ Configuration manager initialized for postgresql-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 04:04:42.134 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for postgresql-cluster
INFO:     127.0.0.1:57006 - "GET /api/v1/secrets/postgresql-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:60588 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 04:04:59.812 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: redis-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for redis-cluster
INFO:app.config_manager:✅ Configuration manager initialized for redis-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 04:04:59.866 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for redis-cluster
INFO:     127.0.0.1:60596 - "GET /api/v1/secrets/redis-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:55142 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55144 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 04:05:07.065 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: kafka-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for kafka-cluster
INFO:app.config_manager:✅ Configuration manager initialized for kafka-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 04:05:07.080 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for kafka-cluster
INFO:     127.0.0.1:55146 - "GET /api/v1/secrets/kafka-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:40556 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 04:05:17.750 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: minio-storage
INFO:app.config_manager:⚙️ Initializing configuration manager for minio-storage
INFO:app.config_manager:✅ Configuration manager initialized for minio-storage
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 04:05:17.771 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for minio-storage
INFO:     127.0.0.1:40562 - "GET /api/v1/secrets/minio-storage HTTP/1.1" 200 OK
INFO:     127.0.0.1:38428 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 04:05:28.185 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: weaviate-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for weaviate-cluster
INFO:app.config_manager:✅ Configuration manager initialized for weaviate-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 04:05:28.206 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for weaviate-cluster
INFO:     127.0.0.1:38444 - "GET /api/v1/secrets/weaviate-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:41146 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41158 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 04:05:37.806 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: monitoring-stack
INFO:app.config_manager:⚙️ Initializing configuration manager for monitoring-stack
INFO:app.config_manager:✅ Configuration manager initialized for monitoring-stack
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 04:05:37.837 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for monitoring-stack
INFO:     127.0.0.1:41170 - "GET /api/v1/secrets/monitoring-stack HTTP/1.1" 200 OK
INFO:     127.0.0.1:37648 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 04:05:50.177 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: elk-stack
INFO:app.config_manager:⚙️ Initializing configuration manager for elk-stack
INFO:app.config_manager:✅ Configuration manager initialized for elk-stack
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 04:05:50.191 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for elk-stack
INFO:     127.0.0.1:37660 - "GET /api/v1/secrets/elk-stack HTTP/1.1" 200 OK
INFO:     127.0.0.1:47836 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 04:06:00.689 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: performance-optimizer
INFO:app.config_manager:⚙️ Initializing configuration manager for performance-optimizer
INFO:app.config_manager:✅ Configuration manager initialized for performance-optimizer
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 04:06:00.713 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for performance-optimizer
INFO:     127.0.0.1:47840 - "GET /api/v1/secrets/performance-optimizer HTTP/1.1" 200 OK
INFO:     127.0.0.1:52310 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56294 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37170 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36156 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36324 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48144 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45144 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45146 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:45152 - "GET /api/v1/config/user-management HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45168 - "GET /api/v1/secrets/user-management HTTP/1.1" 401 Unauthorized
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:37102 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:46072 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:47798 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:34360 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56714 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55592 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53880 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32894 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33850 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55602 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:39452 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:43046 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:35016 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:46400 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41876 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51000 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42702 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52568 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56814 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48802 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:35630 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:54920 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:35028 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:40110 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42160 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45324 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42958 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59764 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41222 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44304 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:58946 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:53268 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:40314 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:57810 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52684 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34848 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34774 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38610 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49390 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39182 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:58654 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:48196 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:35464 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:56960 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33094 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49000 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53638 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35892 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42086 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58306 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:59612 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:41514 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:41886 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:54288 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36834 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45566 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35722 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37766 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35856 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38924 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:49894 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:48870 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:50656 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:54132 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33320 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34090 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36858 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37552 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49076 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56472 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:53298 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:37674 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:45896 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:45908 - "GET /api/v1/config/user-management HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45910 - "GET /api/v1/secrets/user-management HTTP/1.1" 401 Unauthorized
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:50618 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:47112 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35308 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41632 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60090 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56568 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46876 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58942 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:51426 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:53860 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:53874 - "GET /api/v1/config/user-management HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53890 - "GET /api/v1/secrets/user-management HTTP/1.1" 401 Unauthorized
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:60982 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:39748 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:49154 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33592 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53250 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42660 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59048 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42252 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50476 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:41776 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:38800 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:45966 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:34296 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56846 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55412 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40686 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57296 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37796 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41932 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:55032 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:52756 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:51598 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:50126 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44902 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39092 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52262 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48608 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51552 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53364 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:59334 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:33516 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:32962 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:52798 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42472 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38240 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55638 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39260 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32846 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47094 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:35518 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:50250 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:57848 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:37676 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39664 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38228 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43026 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39816 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36090 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49774 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:51298 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:47482 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:46726 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:33638 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37858 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49462 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34820 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58664 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36940 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36298 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:56248 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:52906 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:46160 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:50636 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42706 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33766 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53668 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59884 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56850 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54816 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:47484 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:46192 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:35036 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:51200 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59244 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40008 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54932 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56442 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54102 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53356 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:34402 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:49986 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:48066 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:48220 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36898 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39532 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60854 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39176 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55656 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38168 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:58722 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:33156 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:49580 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:51330 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50340 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59586 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35902 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42316 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55916 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40370 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:58180 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:47198 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:54032 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:35428 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51200 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38670 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37802 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53910 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54160 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49466 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:41510 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:43692 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:51662 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:38708 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58708 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42410 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43082 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44906 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49228 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56448 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:41432 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:58824 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:34848 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:60370 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36050 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46868 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37920 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46794 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42976 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37734 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37746 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:37760 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:29:51.114 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:29:51.132 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:37774 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 06:29:51.135 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:29:51.145 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:37786 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:35344 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:59640 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:41150 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:52696 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42522 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49816 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53844 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54918 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50418 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38906 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:44266 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:60764 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:37058 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:39430 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39442 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:36:46.501 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:36:46.516 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:39444 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:39452 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:36:46.575 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:36:46.587 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:39464 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:45016 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:37:02.371 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:37:02.382 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:45020 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:45026 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:37:02.437 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:37:02.447 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:45038 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:60124 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39270 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39284 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:37:26.899 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:37:26.910 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:39292 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 06:37:26.914 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:37:26.932 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:39306 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:60662 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48488 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48500 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:48510 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:38:19.346 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:38:19.388 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:48520 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 06:38:19.431 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:38:19.456 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:48522 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:52676 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52586 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59696 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:44616 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:46514 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:39582 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:60294 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:60284 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:41:25.858 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:41:25.868 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:60300 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 06:41:25.870 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:41:25.879 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:60304 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:48034 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:34554 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:41:56.478 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:41:56.489 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:34562 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:34564 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:41:56.649 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:41:56.658 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:34570 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:34102 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:51176 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:42:29.795 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:42:29.809 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:51184 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:51186 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:42:29.885 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:42:29.901 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:51196 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:59280 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35742 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:43:07.205 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:43:07.214 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:35754 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:35756 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:43:07.307 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:43:07.320 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:35762 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:58576 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:58592 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:58594 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:43:19.505 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:43:19.518 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:58600 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 06:43:19.532 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:43:19.543 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:58604 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:45686 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:45688 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:43:32.323 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:43:32.335 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:45700 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 06:43:32.337 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:43:32.352 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:45710 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:53954 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47848 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47864 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:47876 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:44:15.480 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:44:15.508 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:47892 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 06:44:15.559 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:44:15.609 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:47896 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:39658 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:44:32.320 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:44:32.339 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:39660 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:39662 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:44:32.481 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:44:32.492 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:39676 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:58560 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58574 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:58570 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:44:48.609 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:44:48.652 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:58584 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 06:44:48.658 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:44:48.685 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:58600 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:56462 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:45:03.300 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:45:03.319 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:56470 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:56484 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:45:03.538 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:45:03.559 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:56486 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:38874 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38886 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:38896 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:45:17.007 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:45:17.020 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:38912 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 06:45:17.045 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:45:17.057 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:38926 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:59026 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:45:34.327 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:45:34.341 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:59042 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:59058 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:45:34.544 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:45:34.578 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:59070 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:57196 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:57198 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:45:51.003 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:45:51.020 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:57212 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:57228 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:45:51.120 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:45:51.165 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:57238 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:55454 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:55458 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:46:07.658 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:46:07.671 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:55468 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 06:46:07.695 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:46:07.713 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:55478 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:33088 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:39084 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39100 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
2025-06-25 06:46:29.792 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:46:29.803 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:39106 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 06:46:29.807 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:46:29.818 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:39114 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:39720 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:52596 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:59054 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:39638 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:42974 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50430 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:53300 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:57454 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:58786 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:58798 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:50:26.586 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:50:26.606 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:58814 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 06:50:26.611 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:50:26.632 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:58822 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:49306 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:37820 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:51:00.336 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:51:00.361 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:37836 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:37840 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:51:00.455 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:51:00.474 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:37846 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:34110 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:33424 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:51:31.502 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:51:31.515 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:33432 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:33442 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:51:31.651 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:51:31.670 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:33444 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:48294 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:47310 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:58554 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:52534 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:50948 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:50956 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:53:36.025 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:53:36.040 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:50964 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 06:53:36.044 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:53:36.061 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:50978 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:56796 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55420 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:54562 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:54:28.165 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:54:28.183 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:54576 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:54592 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:54:28.375 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:54:28.388 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:54594 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:50170 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50180 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:54:48.521 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:54:48.536 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:50190 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:50196 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:54:48.800 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:54:48.811 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:50212 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:56778 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:55:04.348 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:55:04.373 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:56782 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:56788 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 06:55:04.496 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 06:55:04.509 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:56802 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:35618 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:59546 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:60702 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:53206 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:48128 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:42674 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:48144 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:50914 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45480 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:57514 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:49522 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:53908 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:36014 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:58518 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:47746 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:56304 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:38768 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:37864 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39246 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:46890 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:49324 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:39390 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:53260 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:47700 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:43500 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:55262 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:42776 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:51224 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50032 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:46888 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:39898 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:40756 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:53598 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:40424 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:53560 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:55786 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:39466 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:39262 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54650 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:34450 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:60562 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:48210 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:42996 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:41068 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:43150 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43166 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:43180 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 07:17:16.630 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:17:16.654 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:43196 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 07:17:16.660 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:17:16.680 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:43198 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:52038 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:52036 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 07:17:33.275 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:17:33.288 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:52050 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 07:17:33.291 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:17:33.304 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:52058 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:32946 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32960 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:32968 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 07:17:47.672 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:17:47.685 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:32984 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 07:17:47.687 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:17:47.698 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:32994 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:39842 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 07:18:01.719 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:18:01.738 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:39858 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:39874 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 07:18:01.899 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:18:01.977 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:39886 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:37416 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:47092 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:47102 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 07:18:25.045 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:18:25.064 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:47110 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 07:18:25.080 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:18:25.098 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:47112 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:49382 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 07:18:38.469 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:18:38.484 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:49388 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:49402 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 07:18:38.585 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:18:38.597 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:49406 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:39740 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59066 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 07:19:02.385 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:19:02.423 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:59082 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:59092 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 07:19:02.521 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:19:02.540 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:59100 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:41222 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:59576 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:41666 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:56884 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:51274 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:44232 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:37512 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:38274 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:42516 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:47840 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:41202 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:46396 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:50732 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:49898 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:34068 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:51732 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:59752 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 07:27:04.387 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:27:04.396 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:59758 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:58958 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:50588 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:37776 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:35342 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:59266 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:60296 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:36402 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:36418 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:36424 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 07:30:20.121 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:30:20.147 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:36428 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
2025-06-25 07:30:20.155 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:30:20.170 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:36440 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:41660 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 07:30:34.824 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:30:34.838 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:41670 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:     127.0.0.1:41676 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-25 07:30:35.065 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: billing
INFO:app.config_manager:⚙️ Initializing configuration manager for billing
INFO:app.config_manager:✅ Configuration manager initialized for billing
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-25 07:30:35.085 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for billing
INFO:     127.0.0.1:41678 - "GET /api/v1/secrets/billing HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:35634 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:42802 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:60286 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:56900 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:40532 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:37726 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:51272 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:33524 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:46658 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:41478 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:53714 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:52776 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:36084 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:49116 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:39640 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:54106 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:58102 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:50106 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:35104 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:35820 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:51906 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:41558 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:38858 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:36604 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:54230 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:49232 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:54558 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:40746 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:54780 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:37552 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:34346 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:57580 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:56042 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:40318 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:53560 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:42406 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:39436 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:38920 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:48078 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:51426 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:44328 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:33008 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:59660 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:44934 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:53240 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:34404 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:35458 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:55960 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:46658 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:45304 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:44342 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:48868 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:41312 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
