"""
Database configuration and connection management for the Billing Service.

Provides optimized database connections, connection pooling, and performance monitoring.
"""

import os
import asyncio
from typing import Dict, Any, Optional, AsyncGenerator
from contextlib import asynccontextmanager
from sqlalchemy import create_engine, event, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool, NullPool
from sqlalchemy.engine import Engine
import structlog

# from app.core.monitoring import metrics_collector, track_db_query  # DISABLED: Missing dependencies
from app.core.exceptions import DatabaseError

logger = structlog.get_logger(__name__)


class DatabaseConfig:
    """Database configuration management."""
    
    def __init__(self):
        self.database_url = self._get_database_url()
        self.async_database_url = self._get_async_database_url()
        self.pool_config = self._get_pool_config()
        self.connection_config = self._get_connection_config()
    
    def _get_database_url(self) -> str:
        """Get synchronous database URL."""
        return os.getenv(
            "DATABASE_URL",
            "postgresql://billing_user:billing_pass@localhost:5432/billing_db"
        )
    
    def _get_async_database_url(self) -> str:
        """Get asynchronous database URL."""
        sync_url = self._get_database_url()
        if sync_url.startswith("postgresql://"):
            return sync_url.replace("postgresql://", "postgresql+asyncpg://", 1)
        return sync_url
    
    def _get_pool_config(self) -> Dict[str, Any]:
        """Get connection pool configuration."""
        return {
            "pool_size": int(os.getenv("DB_POOL_SIZE", "10")),
            "max_overflow": int(os.getenv("DB_MAX_OVERFLOW", "20")),
            "pool_timeout": int(os.getenv("DB_POOL_TIMEOUT", "30")),
            "pool_recycle": int(os.getenv("DB_POOL_RECYCLE", "3600")),
            "pool_pre_ping": True,
            "poolclass": QueuePool
        }
    
    def _get_connection_config(self) -> Dict[str, Any]:
        """Get connection configuration."""
        return {
            "echo": os.getenv("DB_ECHO", "false").lower() == "true",
            "echo_pool": os.getenv("DB_ECHO_POOL", "false").lower() == "true",
            "future": True,
            "connect_args": {
                "connect_timeout": int(os.getenv("DB_CONNECT_TIMEOUT", "10")),
                "command_timeout": int(os.getenv("DB_COMMAND_TIMEOUT", "60")),
                "server_settings": {
                    "application_name": "billing_service",
                    "jit": "off"  # Disable JIT for better performance on small queries
                }
            }
        }


class DatabaseManager:
    """Centralized database connection and session management."""
    
    def __init__(self):
        self.config = DatabaseConfig()
        self.sync_engine: Optional[Engine] = None
        self.async_engine = None
        self.sync_session_factory = None
        self.async_session_factory = None
        self._connection_stats = {
            "total_connections": 0,
            "active_connections": 0,
            "idle_connections": 0
        }
    
    def initialize_sync_engine(self) -> Engine:
        """Initialize synchronous database engine."""
        if self.sync_engine is None:
            try:
                self.sync_engine = create_engine(
                    self.config.database_url,
                    **self.config.pool_config,
                    **self.config.connection_config
                )
                
                # Add event listeners for monitoring
                self._setup_sync_event_listeners()
                
                # Create session factory
                self.sync_session_factory = sessionmaker(
                    bind=self.sync_engine,
                    autocommit=False,
                    autoflush=False,
                    expire_on_commit=False
                )
                
                logger.info("Synchronous database engine initialized")
                
            except Exception as e:
                logger.error("Failed to initialize sync database engine", error=str(e))
                raise DatabaseError(
                    "Failed to initialize database connection",
                    operation="engine_initialization",
                    details={"error": str(e)}
                )
        
        return self.sync_engine
    
    def initialize_async_engine(self):
        """Initialize asynchronous database engine."""
        if self.async_engine is None:
            try:
                self.async_engine = create_async_engine(
                    self.config.async_database_url,
                    **self.config.pool_config,
                    **self.config.connection_config
                )
                
                # Add event listeners for monitoring
                self._setup_async_event_listeners()
                
                # Create async session factory
                self.async_session_factory = async_sessionmaker(
                    bind=self.async_engine,
                    class_=AsyncSession,
                    autocommit=False,
                    autoflush=False,
                    expire_on_commit=False
                )
                
                logger.info("Asynchronous database engine initialized")
                
            except Exception as e:
                logger.error("Failed to initialize async database engine", error=str(e))
                raise DatabaseError(
                    "Failed to initialize async database connection",
                    operation="async_engine_initialization",
                    details={"error": str(e)}
                )
        
        return self.async_engine
    
    def _setup_sync_event_listeners(self):
        """Setup event listeners for synchronous engine."""
        @event.listens_for(self.sync_engine, "connect")
        def on_connect(dbapi_connection, connection_record):
            self._connection_stats["total_connections"] += 1
            self._connection_stats["active_connections"] += 1
            self._update_connection_metrics()
            
            # Set connection-level optimizations
            with dbapi_connection.cursor() as cursor:
                cursor.execute("SET statement_timeout = '60s'")
                cursor.execute("SET lock_timeout = '30s'")
                cursor.execute("SET idle_in_transaction_session_timeout = '300s'")
        
        @event.listens_for(self.sync_engine, "close")
        def on_close(dbapi_connection, connection_record):
            self._connection_stats["active_connections"] -= 1
            self._update_connection_metrics()
        
        @event.listens_for(self.sync_engine, "checkout")
        def on_checkout(dbapi_connection, connection_record, connection_proxy):
            self._connection_stats["idle_connections"] -= 1
            self._update_connection_metrics()
        
        @event.listens_for(self.sync_engine, "checkin")
        def on_checkin(dbapi_connection, connection_record):
            self._connection_stats["idle_connections"] += 1
            self._update_connection_metrics()
    
    def _setup_async_event_listeners(self):
        """Setup event listeners for asynchronous engine."""
        @event.listens_for(self.async_engine.sync_engine, "connect")
        def on_async_connect(dbapi_connection, connection_record):
            self._connection_stats["total_connections"] += 1
            self._connection_stats["active_connections"] += 1
            self._update_connection_metrics()
    
    def _update_connection_metrics(self):
        """Update connection pool metrics."""
        # metrics_collector.update_db_metrics(  # DISABLED: Missing dependencies
        #     active_connections=self._connection_stats["active_connections"],
        #     idle_connections=self._connection_stats["idle_connections"],
        #     total_connections=self._connection_stats["total_connections"]
        # )
    
    @asynccontextmanager
    async def get_async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get async database session with automatic cleanup."""
        if self.async_session_factory is None:
            self.initialize_async_engine()
        
        async with self.async_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception as e:
                await session.rollback()
                logger.error("Database session error", error=str(e))
                raise DatabaseError(
                    "Database session error",
                    operation="session_management",
                    details={"error": str(e)}
                )
            finally:
                await session.close()
    
    @asynccontextmanager
    async def get_sync_session(self) -> Session:
        """Get sync database session with automatic cleanup."""
        if self.sync_session_factory is None:
            self.initialize_sync_engine()
        
        session = self.sync_session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error("Database session error", error=str(e))
            raise DatabaseError(
                "Database session error",
                operation="sync_session_management",
                details={"error": str(e)}
            )
        finally:
            session.close()
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform database health check."""
        try:
            if self.async_engine is None:
                self.initialize_async_engine()
            
            async with self.async_engine.begin() as conn:
                result = await conn.execute(text("SELECT 1"))
                result.fetchone()
            
            # Get connection pool status
            pool = self.async_engine.pool
            pool_status = {
                "size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "invalid": pool.invalid()
            }
            
            return {
                "status": "healthy",
                "connection_pool": pool_status,
                "database_url": self.config.database_url.split("@")[1] if "@" in self.config.database_url else "unknown"
            }
            
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def close_connections(self):
        """Close all database connections."""
        try:
            if self.async_engine:
                await self.async_engine.dispose()
                logger.info("Async database connections closed")
            
            if self.sync_engine:
                self.sync_engine.dispose()
                logger.info("Sync database connections closed")
                
        except Exception as e:
            logger.error("Error closing database connections", error=str(e))


# Global database manager instance
db_manager = DatabaseManager()


# Dependency functions for FastAPI
async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """FastAPI dependency for async database sessions."""
    async with db_manager.get_async_session() as session:
        yield session


def get_sync_db() -> Session:
    """FastAPI dependency for sync database sessions."""
    with db_manager.get_sync_session() as session:
        yield session


# Database initialization function
async def initialize_database():
    """Initialize database connections and perform startup checks."""
    try:
        # Initialize engines
        db_manager.initialize_async_engine()
        db_manager.initialize_sync_engine()
        
        # Perform health check
        health = await db_manager.health_check()
        if health["status"] != "healthy":
            raise DatabaseError(
                "Database health check failed",
                operation="initialization",
                details=health
            )
        
        logger.info("Database initialized successfully", health=health)
        
    except Exception as e:
        logger.error("Database initialization failed", error=str(e))
        raise


# Database cleanup function
async def cleanup_database():
    """Cleanup database connections on shutdown."""
    await db_manager.close_connections()


# Query optimization utilities
class QueryOptimizer:
    """Database query optimization utilities."""
    
    @staticmethod
    async def execute_with_retry(
        session: AsyncSession,
        query,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ):
        """Execute query with retry logic for transient failures."""
        for attempt in range(max_retries):
            try:
                # async with track_db_query("execute", "unknown"):  # DISABLED: Missing dependencies
                result = await session.execute(query)
                return result
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                
                logger.warning(
                    "Query execution failed, retrying",
                    attempt=attempt + 1,
                    error=str(e)
                )
                await asyncio.sleep(retry_delay * (2 ** attempt))
    
    @staticmethod
    def optimize_query_for_pagination(
        query,
        offset: int,
        limit: int,
        max_limit: int = 100
    ):
        """Optimize query for pagination."""
        # Limit the maximum number of results
        safe_limit = min(limit, max_limit)
        
        # Add offset and limit
        return query.offset(offset).limit(safe_limit)
    
    @staticmethod
    def add_query_hints(query, hints: Dict[str, Any]):
        """Add database-specific query hints for optimization."""
        # PostgreSQL-specific optimizations
        if "use_index" in hints:
            # This would be implemented based on specific query patterns
            pass
        
        if "parallel" in hints and hints["parallel"]:
            # Enable parallel query execution
            query = query.execution_options(
                postgresql_readonly=True,
                postgresql_isolation_level="AUTOCOMMIT"
            )
        
        return query
