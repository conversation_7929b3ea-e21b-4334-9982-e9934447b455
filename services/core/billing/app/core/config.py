"""
Configuration settings for the Billing Service.
"""

import os
from typing import Optional


def get_vault_config(key: str, default: str = None) -> str:
    """
    Get configuration from Vault or environment variables.
    
    Args:
        key: Configuration key
        default: Default value if not found
        
    Returns:
        Configuration value
    """
    # TODO: Implement actual Vault integration
    # For now, use environment variables
    return os.getenv(key, default)


def get_vault_secret(key: str, default: str = None) -> str:
    """
    Get secret from Vault or environment variables.
    
    Args:
        key: Secret key
        default: Default value if not found
        
    Returns:
        Secret value
    """
    # TODO: Implement actual Vault integration
    # For now, use environment variables
    return os.getenv(key, default)


class Settings:
    """Application settings."""
    
    # Service Configuration
    SERVICE_NAME: str = "billing-service"
    SERVICE_VERSION: str = "1.0.0"
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    
    # PostgreSQL Cluster Service Configuration
    POSTGRESQL_CLUSTER_URL: str = get_vault_config(
        "POSTGRESQL_CLUSTER_URL", 
        "http://localhost:7234"
    )
    
    # Database Configuration (disabled - using PostgreSQL Cluster Service)
    DATABASE_NAME: str = get_vault_config("DATABASE_NAME", "simba_billing_database")
    DATABASE_URL: str = "********************************************/disabled"
    
    # Redis for caching and sessions
    REDIS_URL: str = get_vault_secret("REDIS_URL", "redis://localhost:6379/0")
    
    # MongoDB for additional data
    MONGODB_URL: str = get_vault_secret(
        "MONGODB_URL",
        "*******************************************************"
    )
    
    # JWT Configuration
    JWT_SECRET_KEY: str = get_vault_secret("JWT_SECRET_KEY", "your-secret-key-here")
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Rate Limiting
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_REQUESTS_PER_MINUTE: int = 60
    RATE_LIMIT_BURST: int = 10
    
    # External Services
    STRIPE_SECRET_KEY: str = get_vault_secret("STRIPE_SECRET_KEY", "")
    STRIPE_WEBHOOK_SECRET: str = get_vault_secret("STRIPE_WEBHOOK_SECRET", "")
    
    # Service Registry
    SERVICE_REGISTRY_URL: str = get_vault_config(
        "SERVICE_REGISTRY_URL", 
        "http://localhost:7230"
    )
    
    # Monitoring
    METRICS_ENABLED: bool = True
    HEALTH_CHECK_INTERVAL: int = 30
    
    # Logging
    LOG_LEVEL: str = get_vault_config("LOG_LEVEL", "INFO")
    LOG_FORMAT: str = "json"
    
    # CORS
    CORS_ORIGINS: list = [
        "http://localhost:3000",
        "http://localhost:8000",
        "https://simba-ai.com"
    ]
    
    # Security
    ALLOWED_HOSTS: list = ["*"]  # Configure properly for production
    
    # Billing Configuration
    DEFAULT_CURRENCY: str = "USD"
    INVOICE_DUE_DAYS: int = 30
    TRIAL_PERIOD_DAYS: int = 14
    
    # Usage Tracking
    USAGE_AGGREGATION_INTERVAL: int = 3600  # 1 hour in seconds
    USAGE_RETENTION_DAYS: int = 365
    
    # Payment Processing
    PAYMENT_RETRY_ATTEMPTS: int = 3
    PAYMENT_RETRY_DELAY: int = 300  # 5 minutes in seconds


# Global settings instance
settings = Settings()
