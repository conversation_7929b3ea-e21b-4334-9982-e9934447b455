"""
Database initialization via PostgreSQL Cluster Service for Billing Service.

This module initializes the billing database using the PostgreSQL Cluster Service
instead of direct database connections.
"""

import logging
from typing import Dict, Any
from uuid import uuid4

from app.services.postgresql_client import postgresql_client

logger = logging.getLogger(__name__)


async def initialize_database_via_cluster() -> bool:
    """Initialize the billing database via PostgreSQL Cluster Service."""
    try:
        logger.info("🚀 Starting billing service initialization via PostgreSQL Cluster Service...")

        # Check if PostgreSQL Cluster Service is available
        if not await postgresql_client.health_check():
            logger.error("❌ PostgreSQL Cluster Service is not available")
            return False

        # Note: Tables and schema are created by the PostgreSQL Cluster Service during its startup
        # We only need to create any billing-specific data here if needed
        logger.info("📋 Tables and schema are managed by PostgreSQL Cluster Service - skipping creation")

        # Initialize billing-specific data if needed
        if not await initialize_billing_data():
            logger.error("❌ Failed to initialize billing data")
            return False

        logger.info("✅ Billing service initialization completed successfully via PostgreSQL Cluster Service")
        return True

    except Exception as e:
        logger.error(f"❌ Billing service initialization failed: {e}")
        return False


async def initialize_billing_data() -> bool:
    """Initialize billing-specific data."""
    try:
        logger.info("🏗️ Initializing billing-specific data...")

        # Create default subscription plans if they don't exist
        if not await create_default_subscription_plans():
            logger.error("❌ Failed to create default subscription plans")
            return False

        logger.info("✅ Billing data initialization completed")
        return True

    except Exception as e:
        logger.error(f"❌ Billing data initialization failed: {e}")
        return False


async def create_default_subscription_plans() -> bool:
    """Create default subscription plans."""
    try:
        logger.info("📋 Creating default subscription plans...")

        # Check if plans already exist
        existing_plans = await postgresql_client.select_records(
            "subscription_plans",
            columns=["id", "name"],
            limit=1
        )

        if existing_plans and existing_plans.get("success") and existing_plans.get("data"):
            logger.info("📋 Subscription plans already exist - skipping creation")
            return True

        # Default subscription plans
        default_plans = [
            {
                "id": str(uuid4()),
                "name": "Free",
                "description": "Free tier with basic features",
                "price": 0.00,
                "currency": "USD",
                "interval": "month",
                "interval_count": 1,
                "trial_period_days": 0,
                "is_active": True,
                "plan_metadata": {
                    "tier": "free",
                    "default": True,
                    "features": {
                        "api_requests": 1000,
                        "chat_conversations": 50,
                        "tts_minutes": 10,
                        "image_generations": 5
                    }
                }
            },
            {
                "id": str(uuid4()),
                "name": "Pro",
                "description": "Professional tier with advanced features",
                "price": 29.99,
                "currency": "USD",
                "interval": "month",
                "interval_count": 1,
                "trial_period_days": 14,
                "is_active": True,
                "plan_metadata": {
                    "tier": "pro",
                    "popular": True,
                    "features": {
                        "api_requests": 50000,
                        "chat_conversations": 1000,
                        "tts_minutes": 500,
                        "image_generations": 200
                    }
                }
            },
            {
                "id": str(uuid4()),
                "name": "Enterprise",
                "description": "Enterprise tier with unlimited features",
                "price": 99.99,
                "currency": "USD",
                "interval": "month",
                "interval_count": 1,
                "trial_period_days": 30,
                "is_active": True,
                "plan_metadata": {
                    "tier": "enterprise",
                    "custom_support": True,
                    "features": {
                        "api_requests": -1,  # Unlimited
                        "chat_conversations": -1,  # Unlimited
                        "tts_minutes": -1,  # Unlimited
                        "image_generations": -1  # Unlimited
                    }
                }
            }
        ]

        # Create each plan
        for plan_data in default_plans:
            result = await postgresql_client.insert_record(
                "subscription_plans",
                plan_data
            )

            if not result or not result.get("success"):
                logger.error(f"❌ Failed to create subscription plan: {plan_data['name']}")
                return False

            logger.info(f"✅ Created subscription plan: {plan_data['name']}")

        logger.info("✅ Default subscription plans created successfully")
        return True

    except Exception as e:
        logger.error(f"❌ Error creating default subscription plans: {e}")
        return False


async def create_default_pricing_rules() -> bool:
    """Create default pricing rules for usage-based billing."""
    try:
        logger.info("📋 Creating default pricing rules...")

        # Check if pricing rules already exist
        existing_rules = await postgresql_client.select_records(
            "pricing_rules",
            columns=["id", "service"],
            limit=1
        )

        if existing_rules and existing_rules.get("success") and existing_rules.get("data"):
            logger.info("📋 Pricing rules already exist - skipping creation")
            return True

        # Default pricing rules
        default_rules = [
            {
                "id": str(uuid4()),
                "service": "chat",
                "usage_type": "conversations",
                "price_per_unit": 0.01,
                "currency": "USD",
                "tier": "free",
                "is_active": True,
                "metadata": {"description": "Chat conversations for free tier"}
            },
            {
                "id": str(uuid4()),
                "service": "api",
                "usage_type": "requests",
                "price_per_unit": 0.001,
                "currency": "USD",
                "tier": "pro",
                "is_active": True,
                "metadata": {"description": "API requests for pro tier"}
            },
            {
                "id": str(uuid4()),
                "service": "tts",
                "usage_type": "minutes",
                "price_per_unit": 0.15,
                "currency": "USD",
                "tier": "all",
                "is_active": True,
                "metadata": {"description": "Text-to-speech minutes"}
            },
            {
                "id": str(uuid4()),
                "service": "image",
                "usage_type": "generations",
                "price_per_unit": 0.02,
                "currency": "USD",
                "tier": "all",
                "is_active": True,
                "metadata": {"description": "Image generations"}
            }
        ]

        # Create each pricing rule
        for rule_data in default_rules:
            result = await postgresql_client.insert_record(
                "pricing_rules",
                rule_data
            )

            if not result or not result.get("success"):
                logger.error(f"❌ Failed to create pricing rule: {rule_data['service']}/{rule_data['usage_type']}")
                return False

            logger.info(f"✅ Created pricing rule: {rule_data['service']}/{rule_data['usage_type']}")

        logger.info("✅ Default pricing rules created successfully")
        return True

    except Exception as e:
        logger.error(f"❌ Error creating default pricing rules: {e}")
        return False
