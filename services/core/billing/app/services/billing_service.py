"""
Billing Service Implementation

Handles usage tracking, invoice generation, and billing operations.

NOTE: This service has been migrated to use PostgreSQL Cluster Service instead of direct database access.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from decimal import Decimal
import logging

# NOTE: Direct database imports disabled - using PostgreSQL Cluster Service
# from sqlalchemy.orm import Session
# from sqlalchemy import func
# from app.models.billing import Usage, Invoice, BillingPlan, Subscription
# from app.models.database import get_db

from app.services.postgresql_client import postgresql_client
from app.services.payment_service import payment_service

logger = logging.getLogger(__name__)


class BillingService:
    """
    Core billing service for usage tracking and invoice generation.

    WARNING: This service uses direct database access and needs to be migrated
    to use PostgreSQL Cluster Service. Currently may cause connection errors.
    """
    
    def __init__(self):
        self.initialized = False
        self.usage_cache = {}
        self.billing_plans = {}
    
    async def initialize(self):
        """Initialize the billing service."""
        try:
            logger.info("Initializing Billing Service...")
            
            # Load billing plans
            await self._load_billing_plans()
            
            # Start background tasks
            asyncio.create_task(self._usage_aggregation_task())
            asyncio.create_task(self._invoice_generation_task())
            
            self.initialized = True
            logger.info("✅ Billing Service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Billing Service: {str(e)}")
            raise
    
    async def cleanup(self):
        """Cleanup billing service resources."""
        logger.info("Cleaning up Billing Service...")
        self.initialized = False
    
    async def health_check(self) -> Dict[str, Any]:
        """Check billing service health."""
        return {
            "status": "healthy" if self.initialized else "unhealthy",
            "initialized": self.initialized,
            "billing_plans_loaded": len(self.billing_plans),
            "usage_cache_size": len(self.usage_cache)
        }
    
    async def _load_billing_plans(self):
        """Load billing plans from database."""
        try:
            # Default billing plans
            self.billing_plans = {
                "free": {
                    "name": "Free Plan",
                    "monthly_tokens": 10000,
                    "price_per_month": Decimal("0.00"),
                    "price_per_token": Decimal("0.00"),
                    "features": ["basic_chat", "limited_ai_tools"]
                },
                "pro": {
                    "name": "Pro Plan",
                    "monthly_tokens": 100000,
                    "price_per_month": Decimal("29.99"),
                    "price_per_token": Decimal("0.0003"),
                    "features": ["unlimited_chat", "all_ai_tools", "priority_support"]
                },
                "enterprise": {
                    "name": "Enterprise Plan",
                    "monthly_tokens": 1000000,
                    "price_per_month": Decimal("299.99"),
                    "price_per_token": Decimal("0.0002"),
                    "features": ["unlimited_everything", "custom_models", "dedicated_support"]
                }
            }
            logger.info(f"Loaded {len(self.billing_plans)} billing plans")
            
        except Exception as e:
            logger.error(f"Failed to load billing plans: {str(e)}")
            raise
    
    async def track_usage(
        self,
        user_id: str,
        service: str,
        usage_type: str,
        amount: int,
        metadata: Optional[Dict] = None
    ) -> bool:
        """
        Track usage for a user.
        
        Args:
            user_id: User identifier
            service: Service name (e.g., 'chat', 'tts', 'image_gen')
            usage_type: Type of usage (e.g., 'tokens', 'requests', 'minutes')
            amount: Amount of usage
            metadata: Additional metadata
            
        Returns:
            True if usage was tracked successfully
        """
        try:
            # Cache usage for batch processing
            cache_key = f"{user_id}:{service}:{usage_type}"
            
            if cache_key not in self.usage_cache:
                self.usage_cache[cache_key] = {
                    "user_id": user_id,
                    "service": service,
                    "usage_type": usage_type,
                    "total_amount": 0,
                    "count": 0,
                    "metadata": metadata or {},
                    "first_tracked": datetime.utcnow(),
                    "last_tracked": datetime.utcnow()
                }
            
            self.usage_cache[cache_key]["total_amount"] += amount
            self.usage_cache[cache_key]["count"] += 1
            self.usage_cache[cache_key]["last_tracked"] = datetime.utcnow()
            
            logger.debug(f"Tracked usage: {cache_key} += {amount}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to track usage: {str(e)}")
            return False
    
    async def get_user_usage(
        self,
        user_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Get usage summary for a user.
        
        Args:
            user_id: User identifier
            start_date: Start date for usage query
            end_date: End date for usage query
            
        Returns:
            Usage summary
        """
        try:
            db = next(get_db())
            
            # Default to current month if no dates provided
            if not start_date:
                start_date = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            if not end_date:
                end_date = datetime.utcnow()
            
            # Query usage from database
            usage_query = db.query(Usage).filter(
                Usage.user_id == user_id,
                Usage.created_at >= start_date,
                Usage.created_at <= end_date
            )
            
            usage_records = usage_query.all()
            
            # Aggregate usage by service and type
            usage_summary = {}
            total_cost = Decimal("0.00")
            
            for record in usage_records:
                service_key = f"{record.service}:{record.usage_type}"
                
                if service_key not in usage_summary:
                    usage_summary[service_key] = {
                        "service": record.service,
                        "usage_type": record.usage_type,
                        "total_amount": 0,
                        "total_cost": Decimal("0.00"),
                        "count": 0
                    }
                
                usage_summary[service_key]["total_amount"] += record.amount
                usage_summary[service_key]["total_cost"] += record.cost or Decimal("0.00")
                usage_summary[service_key]["count"] += 1
                total_cost += record.cost or Decimal("0.00")
            
            return {
                "user_id": user_id,
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "usage_summary": list(usage_summary.values()),
                "total_cost": float(total_cost),
                "total_records": len(usage_records)
            }
            
        except Exception as e:
            logger.error(f"Failed to get user usage: {str(e)}")
            raise
        finally:
            db.close()
    
    async def calculate_cost(
        self,
        user_id: str,
        service: str,
        usage_type: str,
        amount: int
    ) -> Decimal:
        """
        Calculate cost for usage based on user's billing plan.
        
        Args:
            user_id: User identifier
            service: Service name
            usage_type: Type of usage
            amount: Amount of usage
            
        Returns:
            Cost in decimal
        """
        try:
            # Get user's billing plan (default to free)
            user_plan = await self._get_user_billing_plan(user_id)
            plan_config = self.billing_plans.get(user_plan, self.billing_plans["free"])
            
            # Calculate cost based on usage type
            if usage_type == "tokens":
                cost = Decimal(str(amount)) * plan_config["price_per_token"]
            elif usage_type == "requests":
                # Flat rate per request
                cost = Decimal(str(amount)) * Decimal("0.01")
            elif usage_type == "minutes":
                # Rate per minute for audio/video processing
                cost = Decimal(str(amount)) * Decimal("0.05")
            else:
                # Default rate
                cost = Decimal(str(amount)) * Decimal("0.001")
            
            return cost
            
        except Exception as e:
            logger.error(f"Failed to calculate cost: {str(e)}")
            return Decimal("0.00")
    
    async def _get_user_billing_plan(self, user_id: str) -> str:
        """Get user's current billing plan."""
        try:
            db = next(get_db())
            
            # Query user's active subscription
            subscription = db.query(Subscription).filter(
                Subscription.user_id == user_id,
                Subscription.status == "active"
            ).first()
            
            if subscription:
                return subscription.plan_id
            else:
                return "free"  # Default to free plan
                
        except Exception as e:
            logger.error(f"Failed to get user billing plan: {str(e)}")
            return "free"
        finally:
            db.close()
    
    async def _usage_aggregation_task(self):
        """Background task to aggregate cached usage data."""
        while self.initialized:
            try:
                if self.usage_cache:
                    await self._flush_usage_cache()
                
                # Wait 60 seconds before next aggregation
                await asyncio.sleep(60)
                
            except Exception as e:
                logger.error(f"Usage aggregation task error: {str(e)}")
                await asyncio.sleep(60)
    
    async def _flush_usage_cache(self):
        """Flush cached usage data to database."""
        try:
            db = next(get_db())
            
            # Process cached usage
            for cache_key, usage_data in list(self.usage_cache.items()):
                # Calculate cost
                cost = await self.calculate_cost(
                    usage_data["user_id"],
                    usage_data["service"],
                    usage_data["usage_type"],
                    usage_data["total_amount"]
                )
                
                # Create usage record
                usage_record = Usage(
                    user_id=usage_data["user_id"],
                    service=usage_data["service"],
                    usage_type=usage_data["usage_type"],
                    amount=usage_data["total_amount"],
                    cost=cost,
                    metadata=usage_data["metadata"],
                    created_at=usage_data["last_tracked"]
                )
                
                db.add(usage_record)
                
                # Remove from cache
                del self.usage_cache[cache_key]
            
            db.commit()
            logger.debug("Flushed usage cache to database")
            
        except Exception as e:
            logger.error(f"Failed to flush usage cache: {str(e)}")
            db.rollback()
        finally:
            db.close()
    
    async def _invoice_generation_task(self):
        """Background task to generate monthly invoices."""
        while self.initialized:
            try:
                # Check if it's time to generate invoices (first day of month)
                now = datetime.utcnow()
                if now.day == 1 and now.hour == 0:
                    await self._generate_monthly_invoices()
                
                # Wait 1 hour before next check
                await asyncio.sleep(3600)
                
            except Exception as e:
                logger.error(f"Invoice generation task error: {str(e)}")
                await asyncio.sleep(3600)
    
    async def _generate_monthly_invoices(self):
        """Generate monthly invoices for all users."""
        try:
            logger.info("Generating monthly invoices...")
            
            db = next(get_db())
            
            # Get all users with usage in the previous month
            last_month = datetime.utcnow().replace(day=1) - timedelta(days=1)
            start_of_last_month = last_month.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            end_of_last_month = last_month.replace(hour=23, minute=59, second=59, microsecond=999999)
            
            users_with_usage = db.query(Usage.user_id).filter(
                Usage.created_at >= start_of_last_month,
                Usage.created_at <= end_of_last_month
            ).distinct().all()
            
            for (user_id,) in users_with_usage:
                await self._generate_user_invoice(user_id, start_of_last_month, end_of_last_month)
            
            logger.info(f"Generated invoices for {len(users_with_usage)} users")
            
        except Exception as e:
            logger.error(f"Failed to generate monthly invoices: {str(e)}")
        finally:
            db.close()
    
    async def _generate_user_invoice(
        self,
        user_id: str,
        start_date: datetime,
        end_date: datetime
    ):
        """Generate invoice for a specific user."""
        try:
            # Get user usage for the period
            usage_data = await self.get_user_usage(user_id, start_date, end_date)
            
            if usage_data["total_cost"] > 0:
                db = next(get_db())
                
                # Create invoice
                invoice = Invoice(
                    user_id=user_id,
                    amount=Decimal(str(usage_data["total_cost"])),
                    currency="USD",
                    status="pending",
                    billing_period_start=start_date,
                    billing_period_end=end_date,
                    line_items=usage_data["usage_summary"],
                    created_at=datetime.utcnow()
                )
                
                db.add(invoice)
                db.commit()
                
                logger.info(f"Generated invoice for user {user_id}: ${usage_data['total_cost']}")
                
        except Exception as e:
            logger.error(f"Failed to generate user invoice: {str(e)}")
        finally:
            db.close()


# Global billing service instance
billing_service = BillingService()
