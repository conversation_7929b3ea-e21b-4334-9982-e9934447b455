"""
PostgreSQL Cluster Service Client for Billing Service

Handles communication with the PostgreSQL Cluster microservice for billing operations.
"""

import httpx
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

from app.core.config import settings

logger = logging.getLogger(__name__)


class PostgreSQLClusterClient:
    """Client for communicating with PostgreSQL Cluster Service for billing operations."""

    def __init__(self):
        self.base_url = settings.POSTGRESQL_CLUSTER_URL
        self.service_token = "simbaai-internal-token"  # Should come from Vault
        self.timeout = 30.0

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Dict[str, Any] = None,
        params: Dict[str, Any] = None
    ) -> Optional[Dict[str, Any]]:
        """Make HTTP request to PostgreSQL Cluster Service."""
        url = f"{self.base_url}{endpoint}"
        headers = {
            "Authorization": f"Bearer {self.service_token}",
            "Content-Type": "application/json"
        }

        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                if method.upper() == "GET":
                    response = await client.get(url, headers=headers, params=params)
                elif method.upper() == "POST":
                    response = await client.post(url, headers=headers, json=data)
                elif method.upper() == "PUT":
                    response = await client.put(url, headers=headers, json=data)
                elif method.upper() == "DELETE":
                    response = await client.delete(url, headers=headers, params=params)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

                response.raise_for_status()
                return response.json()

        except httpx.TimeoutException:
            logger.error(f"Timeout calling PostgreSQL Cluster Service: {method} {endpoint}")
            return None
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error calling PostgreSQL Cluster Service: {e.response.status_code} - {e.response.text}")
            return None
        except Exception as e:
            logger.error(f"Error calling PostgreSQL Cluster Service: {str(e)}")
            return None

    # Health Check
    async def health_check(self) -> bool:
        """Check if PostgreSQL Cluster Service is available."""
        try:
            result = await self._make_request("GET", "/health")
            return result is not None and result.get("status") == "healthy"
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False

    # Database Operations
    async def execute_query(self, query: str, params: List[Any] = None) -> Optional[Dict[str, Any]]:
        """Execute a raw SQL query."""
        data = {
            "query": query,
            "params": params or []
        }
        return await self._make_request("POST", "/api/v1/query/execute", data)

    # Record Operations
    async def select_records(
        self,
        table_name: str,
        columns: List[str] = None,
        where_clause: str = None,
        params: List[Any] = None,
        order_by: str = None,
        limit: int = None,
        offset: int = None
    ) -> Optional[Dict[str, Any]]:
        """Select records from a table."""
        data = {
            "table_name": table_name,
            "columns": columns or ["*"],
            "where_clause": where_clause,
            "params": params or [],
            "order_by": order_by,
            "limit": limit,
            "offset": offset
        }
        return await self._make_request("POST", "/api/v1/records/select", data)

    async def insert_record(
        self,
        table_name: str,
        data: Dict[str, Any],
        returning: List[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Insert a record into a table."""
        request_data = {
            "table_name": table_name,
            "data": data,
            "returning": returning or ["*"]
        }
        return await self._make_request("POST", "/api/v1/records/insert", request_data)

    async def update_record(
        self,
        table_name: str,
        data: Dict[str, Any],
        where_clause: str,
        params: List[Any] = None,
        returning: List[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Update records in a table."""
        request_data = {
            "table_name": table_name,
            "data": data,
            "where_clause": where_clause,
            "params": params or [],
            "returning": returning or ["*"]
        }
        return await self._make_request("POST", "/api/v1/records/update", request_data)

    async def delete_record(
        self,
        table_name: str,
        where_clause: str,
        params: List[Any] = None,
        returning: List[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Delete records from a table."""
        request_data = {
            "table_name": table_name,
            "where_clause": where_clause,
            "params": params or [],
            "returning": returning or ["*"]
        }
        return await self._make_request("POST", "/api/v1/records/delete", request_data)

    # Billing-specific Operations
    async def create_usage_record(
        self,
        user_id: str,
        service: str,
        usage_type: str,
        amount: int,
        metadata: Dict[str, Any] = None
    ) -> Optional[Dict[str, Any]]:
        """Create a usage tracking record."""
        data = {
            "user_id": user_id,
            "service": service,
            "usage_type": usage_type,
            "amount": amount,
            "metadata": metadata or {},
            "timestamp": datetime.utcnow().isoformat()
        }
        return await self.insert_record("usage_tracking", data)

    async def get_user_usage(
        self,
        user_id: str,
        start_date: datetime = None,
        end_date: datetime = None,
        service: str = None
    ) -> Optional[Dict[str, Any]]:
        """Get usage records for a user."""
        where_conditions = ["user_id = %s"]
        params = [user_id]

        if start_date:
            where_conditions.append("timestamp >= %s")
            params.append(start_date.isoformat())

        if end_date:
            where_conditions.append("timestamp <= %s")
            params.append(end_date.isoformat())

        if service:
            where_conditions.append("service = %s")
            params.append(service)

        where_clause = " AND ".join(where_conditions)

        return await self.select_records(
            "usage_tracking",
            where_clause=where_clause,
            params=params,
            order_by="timestamp DESC"
        )

    async def create_invoice(
        self,
        user_id: str,
        amount: float,
        currency: str,
        billing_period_start: datetime,
        billing_period_end: datetime,
        metadata: Dict[str, Any] = None
    ) -> Optional[Dict[str, Any]]:
        """Create an invoice record."""
        data = {
            "user_id": user_id,
            "amount": amount,
            "currency": currency,
            "status": "pending",
            "billing_period_start": billing_period_start.isoformat(),
            "billing_period_end": billing_period_end.isoformat(),
            "created_at": datetime.utcnow().isoformat(),
            "metadata": metadata or {}
        }
        return await self.insert_record("invoices", data)

    async def get_user_invoices(
        self,
        user_id: str,
        limit: int = 10,
        offset: int = 0
    ) -> Optional[Dict[str, Any]]:
        """Get invoices for a user."""
        return await self.select_records(
            "invoices",
            where_clause="user_id = %s",
            params=[user_id],
            order_by="created_at DESC",
            limit=limit,
            offset=offset
        )

    async def get_invoice_by_id(
        self,
        invoice_id: str,
        user_id: str = None
    ) -> Optional[Dict[str, Any]]:
        """Get a specific invoice by ID."""
        where_clause = "id = %s"
        params = [invoice_id]

        if user_id:
            where_clause += " AND user_id = %s"
            params.append(user_id)

        result = await self.select_records(
            "invoices",
            where_clause=where_clause,
            params=params
        )

        if result and result.get("success") and result.get("data"):
            return {"success": True, "data": result["data"][0] if result["data"] else None}
        return result


# Global client instance
postgresql_client = PostgreSQLClusterClient()
