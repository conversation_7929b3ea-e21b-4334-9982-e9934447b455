"""
Database connection and session management for Billing Service.

NOTE: Direct database connection is disabled - using PostgreSQL Cluster Service instead.
This is a placeholder to prevent import errors in API routes.
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from typing import Generator
import os
import logging

logger = logging.getLogger(__name__)

# Database configuration - DISABLED (using PostgreSQL Cluster Service)
DATABASE_URL = "********************************************/disabled"

# Create SQLAlchemy engine - DISABLED (using PostgreSQL Cluster Service)
# Only create engine if not using disabled URL to prevent connection attempts
try:
    if "disabled" not in DATABASE_URL:
        engine = create_engine(
            DATABASE_URL,
            pool_pre_ping=True,
            pool_recycle=300,
            echo=False  # Set to True for SQL debugging
        )
    else:
        engine = None
        logger.info("📋 Direct database connection disabled - using PostgreSQL Cluster Service")
except Exception as e:
    engine = None
    logger.warning(f"⚠️ Database engine creation skipped: {e}")

# Create SessionLocal class - DISABLED (using PostgreSQL Cluster Service)
if engine is not None:
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
else:
    SessionLocal = None

# Create Base class for models
Base = declarative_base()


def get_db() -> Generator[Session, None, None]:
    """
    Dependency to get database session.

    NOTE: This is a placeholder - actual database operations use PostgreSQL Cluster Service.

    Yields:
        Session: SQLAlchemy database session (disabled)
    """
    if SessionLocal is None:
        logger.warning("⚠️ Direct database session requested but disabled - using PostgreSQL Cluster Service")
        # Return a mock session to prevent errors
        yield None
        return

    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()


def create_tables():
    """Create all database tables - DISABLED (using PostgreSQL Cluster Service)."""
    logger.info("📋 Table creation handled by PostgreSQL Cluster Service")
    if engine is None:
        logger.info("📋 Direct database connection disabled - tables managed by PostgreSQL Cluster Service")
        return

    try:
        Base.metadata.create_all(bind=engine)
        logger.info("✅ Database tables created successfully")
    except Exception as e:
        logger.error(f"❌ Error creating database tables: {str(e)}")
        raise


def check_database_connection() -> bool:
    """
    Check if database connection is working - DISABLED (using PostgreSQL Cluster Service).

    Returns:
        bool: True if connection is successful
    """
    if engine is None:
        logger.info("📋 Direct database connection disabled - using PostgreSQL Cluster Service")
        return True  # Assume healthy since we're using cluster service

    try:
        with engine.connect() as connection:
            connection.execute("SELECT 1")
        logger.info("✅ Database connection successful")
        return True
    except Exception as e:
        logger.error(f"❌ Database connection failed: {str(e)}")
        return False


# Database health check
async def get_database_health() -> dict:
    """
    Get database health status - DISABLED (using PostgreSQL Cluster Service).

    Returns:
        dict: Database health information
    """
    if engine is None:
        return {
            "status": "healthy",
            "database": "postgresql_cluster_service",
            "version": "managed_by_cluster",
            "url": "cluster_service",
            "connection_pool": {
                "status": "managed_by_cluster_service"
            }
        }

    try:
        with engine.connect() as connection:
            result = connection.execute("SELECT version()")
            version = result.fetchone()[0]

        return {
            "status": "healthy",
            "database": "postgresql",
            "version": version,
            "url": DATABASE_URL.split("@")[1] if "@" in DATABASE_URL else "localhost",
            "connection_pool": {
                "size": engine.pool.size(),
                "checked_in": engine.pool.checkedin(),
                "checked_out": engine.pool.checkedout()
            }
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "database": "postgresql",
            "url": DATABASE_URL.split("@")[1] if "@" in DATABASE_URL else "localhost"
        }
