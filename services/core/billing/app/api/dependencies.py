"""
API Dependencies for Billing Service

Common dependencies used across billing API endpoints.

NOTE: Migrated to use PostgreSQL Cluster Service instead of direct database access.
"""

from fastapi import Depends, HTTPException, status, Header
# from sqlalchemy.orm import Session  # Disabled - using PostgreSQL Cluster Service
from typing import Optional
import jwt
from datetime import datetime, timezone
import logging

logger = logging.getLogger(__name__)


class User:
    """Mock User model for authentication."""
    def __init__(self, id: str, email: str, is_active: bool = True):
        self.id = id
        self.email = email
        self.is_active = is_active


async def get_current_user(
    authorization: Optional[str] = Header(None)
) -> User:
    """
    Get the current authenticated user from JWT token.
    
    Args:
        authorization: Authorization header with Bearer token
        
    Returns:
        User: Current authenticated user
        
    Raises:
        HTTPException: If authentication fails
    """
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization header missing",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        # Extract token from "Bearer <token>"
        scheme, token = authorization.split()
        if scheme.lower() != "bearer":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication scheme",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # In production, validate JWT token properly
        # For now, return a mock user
        mock_user = User(
            id="user_123456789",
            email="<EMAIL>",
            is_active=True
        )
        
        return mock_user
        
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authorization header format",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get the current active user.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        User: Current active user
        
    Raises:
        HTTPException: If user is inactive
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


def verify_api_key(
    x_api_key: Optional[str] = Header(None)
) -> bool:
    """
    Verify API key for webhook endpoints.
    
    Args:
        x_api_key: API key from header
        
    Returns:
        bool: True if API key is valid
        
    Raises:
        HTTPException: If API key is invalid
    """
    if not x_api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key required"
        )
    
    # In production, validate against stored API keys
    valid_api_keys = ["simbaai-webhook-key-123", "simbaai-internal-key-456"]
    
    if x_api_key not in valid_api_keys:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key"
        )
    
    return True


def get_rate_limit_key(
    current_user: User = Depends(get_current_user)
) -> str:
    """
    Get rate limiting key for the current user.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        str: Rate limiting key
    """
    return f"billing_api:{current_user.id}"


class RateLimiter:
    """Simple rate limiter for API endpoints."""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 3600):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}
    
    def is_allowed(self, key: str) -> bool:
        """
        Check if request is allowed under rate limit.
        
        Args:
            key: Rate limiting key
            
        Returns:
            bool: True if request is allowed
        """
        now = datetime.now(timezone.utc)
        
        # Clean old entries
        cutoff = now.timestamp() - self.window_seconds
        self.requests = {
            k: [t for t in timestamps if t > cutoff]
            for k, timestamps in self.requests.items()
        }
        
        # Check current user's requests
        user_requests = self.requests.get(key, [])
        
        if len(user_requests) >= self.max_requests:
            return False
        
        # Add current request
        user_requests.append(now.timestamp())
        self.requests[key] = user_requests
        
        return True


# Global rate limiter instances
standard_rate_limiter = RateLimiter(max_requests=100, window_seconds=3600)  # 100/hour
payment_rate_limiter = RateLimiter(max_requests=10, window_seconds=600)     # 10/10min


def check_standard_rate_limit(
    rate_limit_key: str = Depends(get_rate_limit_key)
) -> bool:
    """
    Check standard rate limit for API endpoints.
    
    Args:
        rate_limit_key: Rate limiting key
        
    Returns:
        bool: True if request is allowed
        
    Raises:
        HTTPException: If rate limit exceeded
    """
    if not standard_rate_limiter.is_allowed(rate_limit_key):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded. Try again later."
        )
    return True


def check_payment_rate_limit(
    rate_limit_key: str = Depends(get_rate_limit_key)
) -> bool:
    """
    Check payment rate limit for payment endpoints.
    
    Args:
        rate_limit_key: Rate limiting key
        
    Returns:
        bool: True if request is allowed
        
    Raises:
        HTTPException: If rate limit exceeded
    """
    if not payment_rate_limiter.is_allowed(rate_limit_key):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Payment rate limit exceeded. Try again in a few minutes."
        )
    return True


def validate_currency(currency: str) -> str:
    """
    Validate currency code.
    
    Args:
        currency: Currency code to validate
        
    Returns:
        str: Validated currency code
        
    Raises:
        HTTPException: If currency is not supported
    """
    supported_currencies = [
        "USD", "EUR", "GBP", "CAD", "AUD", "JPY", "CHF", "SEK", "NOK", "DKK",
        "KES", "UGX", "TZS", "RWF", "ZAR", "NGN", "GHS", "XOF", "XAF", "MAD",
        "EGP", "ETB", "MWK", "ZMW", "BWP", "SZL", "LSL", "NAD", "MZN", "AOA"
    ]
    
    currency_upper = currency.upper()
    if currency_upper not in supported_currencies:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Unsupported currency: {currency}. Supported currencies: {', '.join(supported_currencies)}"
        )
    
    return currency_upper


def validate_amount(amount: float, currency: str = "USD") -> float:
    """
    Validate payment amount based on currency.
    
    Args:
        amount: Amount to validate
        currency: Currency code
        
    Returns:
        float: Validated amount
        
    Raises:
        HTTPException: If amount is invalid
    """
    if amount <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Amount must be greater than zero"
        )
    
    # Currency-specific validation
    currency_limits = {
        "USD": {"min": 0.50, "max": 999999.99},
        "EUR": {"min": 0.50, "max": 999999.99},
        "GBP": {"min": 0.30, "max": 999999.99},
        "KES": {"min": 1.00, "max": 150000.00},
        "JPY": {"min": 50, "max": 99999999}
    }
    
    limits = currency_limits.get(currency, {"min": 0.01, "max": 999999.99})
    
    if amount < limits["min"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Amount too small. Minimum for {currency}: {limits['min']}"
        )
    
    if amount > limits["max"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Amount too large. Maximum for {currency}: {limits['max']}"
        )
    
    return amount
