"""
Payment Processing API Routes

Handles payment processing for multiple providers (Stripe, PayPal, M-Pesa).

NOTE: Migrated to use PostgreSQL Cluster Service instead of direct database access.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Path
# from sqlalchemy.orm import Session  # Disabled - using PostgreSQL Cluster Service
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from decimal import Decimal
from uuid import UUID

# from app.models.database import get_db  # Disabled - using PostgreSQL Cluster Service
from app.services.postgresql_client import postgresql_client
from app.api.dependencies import get_current_user
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


# Pydantic Models
class PaymentProviderResponse(BaseModel):
    """Response model for payment providers."""
    id: str
    name: str
    display_name: str
    supported_currencies: List[str]
    supported_countries: List[str]
    payment_methods: List[str]
    is_active: bool
    configuration: Dict[str, Any]

    class Config:
        from_attributes = True


class CreatePaymentRequest(BaseModel):
    """Request model for creating payments."""
    provider: str = Field(..., description="Payment provider (stripe, paypal, mpesa)")
    amount: Decimal = Field(..., gt=0, description="Payment amount")
    currency: str = Field(..., description="Currency code (USD, EUR, KES)")
    payment_type: str = Field("one_time", description="Payment type (one_time, subscription)")
    description: Optional[str] = Field(None, description="Payment description")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    payment_details: Optional[Dict[str, Any]] = Field(None, description="Provider-specific details")

    class Config:
        schema_extra = {
            "example": {
                "provider": "stripe",
                "amount": 29.99,
                "currency": "USD",
                "payment_type": "one_time",
                "description": "Pro subscription payment",
                "metadata": {
                    "subscription_id": "sub_123",
                    "user_id": "user_456"
                },
                "payment_details": {
                    "payment_method_id": "pm_**********"
                }
            }
        }


class CreatePaymentResponse(BaseModel):
    """Response model for payment creation."""
    success: bool
    payment_id: str
    status: str
    amount: Decimal
    currency: str
    provider: str
    client_secret: Optional[str] = None
    approval_url: Optional[str] = None
    checkout_request_id: Optional[str] = None
    message: str
    next_action: Optional[Dict[str, Any]] = None

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "payment_id": "pay_**********",
                "status": "requires_action",
                "amount": 29.99,
                "currency": "USD",
                "provider": "stripe",
                "client_secret": "pi_**********_secret_abc123",
                "message": "Payment requires 3D Secure authentication",
                "next_action": {
                    "type": "redirect_to_url",
                    "redirect_url": "https://hooks.stripe.com/redirect/..."
                }
            }
        }


class PaymentResponse(BaseModel):
    """Response model for payment details."""
    id: str
    user_id: str
    amount: Decimal
    currency: str
    status: str
    provider: str
    description: Optional[str]
    created_at: datetime
    updated_at: datetime
    metadata: Optional[Dict[str, Any]]

    class Config:
        from_attributes = True


class MPesaPaymentRequest(BaseModel):
    """Request model for M-Pesa payments."""
    phone_number: str = Field(..., description="Phone number in format 254XXXXXXXXX")
    amount: Decimal = Field(..., gt=0, description="Payment amount")
    description: Optional[str] = Field("SimbaAI Payment", description="Payment description")
    account_reference: Optional[str] = Field(None, description="Account reference")

    class Config:
        schema_extra = {
            "example": {
                "phone_number": "************",
                "amount": 1000.00,
                "description": "Pro subscription payment",
                "account_reference": "SUB123"
            }
        }


# Payment Provider Endpoints
@router.get(
    "/providers",
    response_model=List[PaymentProviderResponse],
    tags=["Payment Providers"],
    summary="Get Available Payment Providers",
    description="""
    **Get list of available payment providers**

    Returns all active payment providers with their configurations.

    **Supported Providers:**
    - **Stripe**: Credit/Debit Cards, Bank Transfers, Apple Pay, Google Pay
    - **PayPal**: PayPal Account, Credit Cards, PayPal Credit
    - **M-Pesa**: Mobile Money for Kenya and other supported countries
    """,
    responses={
        200: {"description": "List of payment providers"},
        500: {"description": "Internal server error"}
    }
)
async def get_payment_providers():
    """Get all available payment providers."""
    try:
        # Mock data - replace with actual service call
        providers = [
            {
                "id": "stripe",
                "name": "stripe",
                "display_name": "Stripe",
                "supported_currencies": ["USD", "EUR", "GBP", "CAD", "AUD"],
                "supported_countries": ["US", "CA", "GB", "AU", "DE", "FR"],
                "payment_methods": ["card", "bank_transfer", "apple_pay", "google_pay"],
                "is_active": True,
                "configuration": {
                    "supports_3d_secure": True,
                    "supports_subscriptions": True,
                    "min_amount": 0.50,
                    "max_amount": 999999.99
                }
            },
            {
                "id": "paypal",
                "name": "paypal",
                "display_name": "PayPal",
                "supported_currencies": ["USD", "EUR", "GBP", "CAD", "AUD"],
                "supported_countries": ["US", "CA", "GB", "AU", "DE", "FR"],
                "payment_methods": ["paypal_account", "credit_card", "paypal_credit"],
                "is_active": True,
                "configuration": {
                    "supports_subscriptions": True,
                    "min_amount": 1.00,
                    "max_amount": 10000.00
                }
            },
            {
                "id": "mpesa",
                "name": "mpesa",
                "display_name": "M-Pesa",
                "supported_currencies": ["KES"],
                "supported_countries": ["KE"],
                "payment_methods": ["mobile_money"],
                "is_active": True,
                "configuration": {
                    "supports_subscriptions": False,
                    "min_amount": 1.00,
                    "max_amount": 150000.00
                }
            }
        ]

        return [PaymentProviderResponse(**provider) for provider in providers]

    except Exception as e:
        logger.error(f"Error getting payment providers: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get payment providers"
        )


# Payment Processing Endpoints
@router.post(
    "/",
    response_model=CreatePaymentResponse,
    tags=["Payments"],
    summary="Create Payment",
    description="""
    **Create a new payment with the specified provider**

    Supports multiple payment providers and methods:

    **Stripe Payments:**
    - Credit/Debit Cards with 3D Secure
    - Bank transfers and digital wallets
    - Subscription payments

    **PayPal Payments:**
    - PayPal account payments
    - Credit card processing via PayPal
    - Subscription billing

    **M-Pesa Payments:**
    - Mobile money for Kenya
    - STK Push for seamless payments
    - Real-time payment confirmation

    **Example Request:**
    ```json
    {
        "provider": "stripe",
        "amount": 29.99,
        "currency": "USD",
        "description": "Pro subscription",
        "payment_details": {
            "payment_method_id": "pm_**********"
        }
    }
    ```
    """,
    responses={
        201: {"description": "Payment created successfully"},
        400: {"description": "Invalid request data"},
        402: {"description": "Payment required - insufficient funds"},
        500: {"description": "Internal server error"}
    }
)
async def create_payment(
    request: CreatePaymentRequest,
    current_user = Depends(get_current_user)
):
    """Create a payment with the specified provider."""
    try:
        # Mock response based on provider
        if request.provider == "stripe":
            response_data = {
                "success": True,
                "payment_id": "pay_stripe_**********",
                "status": "requires_action",
                "amount": request.amount,
                "currency": request.currency,
                "provider": "stripe",
                "client_secret": "pi_**********_secret_abc123",
                "message": "Payment requires 3D Secure authentication",
                "next_action": {
                    "type": "redirect_to_url",
                    "redirect_url": "https://hooks.stripe.com/redirect/authenticate"
                }
            }
        elif request.provider == "paypal":
            response_data = {
                "success": True,
                "payment_id": "pay_paypal_**********",
                "status": "pending",
                "amount": request.amount,
                "currency": request.currency,
                "provider": "paypal",
                "approval_url": "https://www.paypal.com/checkoutnow?token=EC-**********",
                "message": "Redirect user to PayPal for approval"
            }
        elif request.provider == "mpesa":
            response_data = {
                "success": True,
                "payment_id": "pay_mpesa_**********",
                "status": "pending",
                "amount": request.amount,
                "currency": request.currency,
                "provider": "mpesa",
                "checkout_request_id": "ws_CO_123456789",
                "message": "STK push sent to user's phone"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported payment provider: {request.provider}"
            )

        return CreatePaymentResponse(**response_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating payment: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create payment"
        )


@router.get(
    "/{payment_id}",
    response_model=PaymentResponse,
    tags=["Payments"],
    summary="Get Payment Details",
    description="""
    **Get detailed information about a specific payment**

    Returns comprehensive payment information including:
    - Payment status and amount
    - Provider information
    - Transaction metadata
    - Creation and update timestamps
    """,
    responses={
        200: {"description": "Payment details"},
        404: {"description": "Payment not found"},
        403: {"description": "Access denied - not your payment"},
        500: {"description": "Internal server error"}
    }
)
async def get_payment(
    payment_id: str = Path(..., description="Payment ID"),
    current_user = Depends(get_current_user)
):
    """Get details for a specific payment."""
    try:
        # Mock response - replace with actual service call
        payment_data = {
            "id": payment_id,
            "user_id": str(current_user.id),
            "amount": Decimal("29.99"),
            "currency": "USD",
            "status": "succeeded",
            "provider": "stripe",
            "description": "Pro subscription payment",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "metadata": {
                "subscription_id": "sub_123",
                "plan_id": "pro"
            }
        }

        return PaymentResponse(**payment_data)

    except Exception as e:
        logger.error(f"Error getting payment {payment_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get payment details"
        )


# M-Pesa Specific Endpoints
@router.post(
    "/mpesa/stk-push",
    response_model=CreatePaymentResponse,
    tags=["M-Pesa Payments"],
    summary="Initiate M-Pesa STK Push",
    description="""
    **Initiate M-Pesa STK Push payment**

    Sends a payment request directly to the user's mobile phone.

    **Process:**
    1. Validates phone number format
    2. Initiates STK push request
    3. Returns checkout request ID
    4. User completes payment on phone
    5. Webhook confirms payment status

    **Phone Number Format:** 254XXXXXXXXX (Kenya)
    """,
    responses={
        201: {"description": "STK push initiated successfully"},
        400: {"description": "Invalid phone number or amount"},
        500: {"description": "Internal server error"}
    }
)
async def initiate_mpesa_payment(
    request: MPesaPaymentRequest,
    current_user = Depends(get_current_user)
):
    """Initiate M-Pesa STK Push payment."""
    try:
        # Validate phone number format
        if not request.phone_number.startswith("254") or len(request.phone_number) != 12:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid phone number format. Use 254XXXXXXXXX"
            )

        # Mock response - replace with actual M-Pesa service call
        response_data = {
            "success": True,
            "payment_id": "pay_mpesa_**********",
            "status": "pending",
            "amount": request.amount,
            "currency": "KES",
            "provider": "mpesa",
            "checkout_request_id": "ws_CO_123456789",
            "message": f"STK push sent to {request.phone_number}. Complete payment on your phone."
        }

        return CreatePaymentResponse(**response_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error initiating M-Pesa payment: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initiate M-Pesa payment"
        )
