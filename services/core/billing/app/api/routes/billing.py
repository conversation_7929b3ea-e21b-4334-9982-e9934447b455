"""
Billing API Routes

Handles billing operations, usage tracking, and invoice management.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from decimal import Decimal
import structlog

from app.models.database import get_db
from app.security.auth import get_current_user, TokenData, Permission, require_permission
from app.security.validation import sanitize_input, validate_uuid
from app.api.dependencies import check_payment_rate_limit, check_standard_rate_limit

logger = structlog.get_logger(__name__)

router = APIRouter()


# Pydantic models
class UsageTrackingRequest(BaseModel):
    """Request model for tracking usage."""
    service: str = Field(..., description="Service name (e.g., 'chat', 'tts', 'image_gen')")
    usage_type: str = Field(..., description="Type of usage (e.g., 'tokens', 'requests', 'minutes')")
    amount: int = Field(..., gt=0, description="Amount of usage")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class UsageResponse(BaseModel):
    """Response model for usage data."""
    user_id: str
    period: Dict[str, str]
    usage_summary: List[Dict[str, Any]]
    total_cost: float
    total_records: int


class InvoiceResponse(BaseModel):
    """Response model for invoice data."""
    id: str
    user_id: str
    amount: float
    currency: str
    status: str
    billing_period_start: datetime
    billing_period_end: datetime
    created_at: datetime
    due_date: Optional[datetime]
    paid_at: Optional[datetime]


@router.post("/track-usage")
@require_permission(Permission.TRACK_USAGE)
async def track_usage(
    request: UsageTrackingRequest,
    current_user: TokenData = Depends(get_current_user),
    db: Session = Depends(get_db),
    _: bool = Depends(check_payment_rate_limit)
):
    """
    Track usage for the authenticated user.

    This endpoint is called by other services to track user usage
    for billing purposes.
    """
    try:
        # Sanitize input data
        sanitized_data = {
            "service": sanitize_input(request.service),
            "usage_type": sanitize_input(request.usage_type),
            "amount": request.amount,
            "metadata": sanitize_input(request.metadata) if request.metadata else None
        }

        logger.info(
            "Usage tracking request",
            user_id=current_user.user_id,
            service=sanitized_data["service"],
            usage_type=sanitized_data["usage_type"],
            amount=sanitized_data["amount"]
        )

        # Mock response - replace with actual service call
        return {
            "success": True,
            "message": "Usage tracked successfully",
            "tracked": {
                "service": sanitized_data["service"],
                "usage_type": sanitized_data["usage_type"],
                "amount": sanitized_data["amount"],
                "user_id": current_user.user_id,
                "timestamp": datetime.utcnow().isoformat()
            }
        }

    except Exception as e:
        logger.error("Error tracking usage", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to track usage"
        )


@router.get("/usage", response_model=UsageResponse)
@require_permission(Permission.VIEW_BILLING)
async def get_usage(
    start_date: Optional[datetime] = Query(None, description="Start date for usage query"),
    end_date: Optional[datetime] = Query(None, description="End date for usage query"),
    current_user: TokenData = Depends(get_current_user),
    db: Session = Depends(get_db),
    _: bool = Depends(check_standard_rate_limit)
):
    """
    Get usage summary for the authenticated user.

    Returns usage data for the specified period or current month if no dates provided.
    """
    try:
        # Validate date range
        if start_date and end_date and start_date > end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date cannot be after end date"
            )

        # Limit date range to prevent abuse
        if start_date and end_date:
            date_diff = end_date - start_date
            if date_diff.days > 365:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Date range cannot exceed 365 days"
                )

        logger.info(
            "Usage data request",
            user_id=current_user.user_id,
            start_date=start_date.isoformat() if start_date else None,
            end_date=end_date.isoformat() if end_date else None
        )

        # Mock response - replace with actual service call
        usage_data = {
            "user_id": current_user.user_id,
            "period": {
                "start": (start_date or datetime.utcnow().replace(day=1)).isoformat(),
                "end": (end_date or datetime.utcnow()).isoformat()
            },
            "usage_summary": [
                {
                    "service": "chat",
                    "usage_type": "conversations",
                    "amount": 45,
                    "cost": 0.00
                },
                {
                    "service": "api",
                    "usage_type": "requests",
                    "amount": 1250,
                    "cost": 12.50
                }
            ],
            "total_cost": 12.50,
            "total_records": 2
        }

        return UsageResponse(**usage_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error getting usage", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get usage data"
        )


@router.get("/usage/current-month", response_model=UsageResponse)
async def get_current_month_usage(
    current_user = Depends(get_current_user)
):
    """
    Get usage summary for the current month.
    """
    try:
        # Get current month dates
        now = datetime.utcnow()
        start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

        # Mock response - replace with actual service call
        usage_data = {
            "user_id": str(current_user.id),
            "period": {
                "start": start_of_month.isoformat(),
                "end": now.isoformat()
            },
            "usage_summary": [
                {
                    "service": "chat",
                    "usage_type": "conversations",
                    "amount": 45,
                    "cost": 0.00
                },
                {
                    "service": "api",
                    "usage_type": "requests",
                    "amount": 1250,
                    "cost": 12.50
                }
            ],
            "total_cost": 12.50,
            "total_records": 2
        }

        return UsageResponse(**usage_data)

    except Exception as e:
        logger.error(f"Error getting current month usage: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get current month usage"
        )


@router.get("/invoices", response_model=List[InvoiceResponse])
async def get_invoices(
    limit: int = Query(10, ge=1, le=100, description="Number of invoices to return"),
    offset: int = Query(0, ge=0, description="Number of invoices to skip"),
    current_user = Depends(get_current_user)
):
    """
    Get invoices for the authenticated user.
    """
    try:
        # Mock response - replace with actual service call
        mock_invoices = [
            {
                "id": "inv_1234567890",
                "user_id": str(current_user.id),
                "amount": 29.99,
                "currency": "USD",
                "status": "paid",
                "billing_period_start": datetime.utcnow() - timedelta(days=30),
                "billing_period_end": datetime.utcnow(),
                "created_at": datetime.utcnow() - timedelta(days=30),
                "due_date": datetime.utcnow() - timedelta(days=15),
                "paid_at": datetime.utcnow() - timedelta(days=15)
            },
            {
                "id": "inv_0987654321",
                "user_id": str(current_user.id),
                "amount": 19.99,
                "currency": "USD",
                "status": "paid",
                "billing_period_start": datetime.utcnow() - timedelta(days=60),
                "billing_period_end": datetime.utcnow() - timedelta(days=30),
                "created_at": datetime.utcnow() - timedelta(days=60),
                "due_date": datetime.utcnow() - timedelta(days=45),
                "paid_at": datetime.utcnow() - timedelta(days=45)
            }
        ]

        # Apply pagination
        paginated_invoices = mock_invoices[offset:offset + limit]

        return [InvoiceResponse(**invoice) for invoice in paginated_invoices]

    except Exception as e:
        logger.error(f"Error getting invoices: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get invoices"
        )


@router.get("/invoices/{invoice_id}", response_model=InvoiceResponse)
@require_permission(Permission.VIEW_INVOICES)
async def get_invoice(
    invoice_id: str,
    current_user: TokenData = Depends(get_current_user),
    db: Session = Depends(get_db),
    _: bool = Depends(check_standard_rate_limit)
):
    """
    Get a specific invoice by ID.
    """
    try:
        # Validate invoice ID format
        sanitized_invoice_id = sanitize_input(invoice_id)

        logger.info(
            "Invoice request",
            user_id=current_user.user_id,
            invoice_id=sanitized_invoice_id
        )

        # Mock response - replace with actual service call
        if sanitized_invoice_id not in ["inv_1234567890", "inv_0987654321"]:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invoice not found"
            )

        mock_invoice = {
            "id": sanitized_invoice_id,
            "user_id": current_user.user_id,
            "amount": 29.99,
            "currency": "USD",
            "status": "paid",
            "billing_period_start": datetime.utcnow() - timedelta(days=30),
            "billing_period_end": datetime.utcnow(),
            "created_at": datetime.utcnow() - timedelta(days=30),
            "due_date": datetime.utcnow() - timedelta(days=15),
            "paid_at": datetime.utcnow() - timedelta(days=15)
        }

        return InvoiceResponse(**mock_invoice)

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error getting invoice", error=str(e), user_id=current_user.user_id, invoice_id=invoice_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get invoice"
        )


@router.get("/plans")
async def get_billing_plans():
    """
    Get available billing plans.
    """
    try:
        # Mock response - replace with actual service call
        return {
            "plans": [
                {
                    "id": "free",
                    "name": "Free",
                    "price": 0.00,
                    "currency": "USD",
                    "features": ["10 conversations/month", "Basic support"]
                },
                {
                    "id": "pro",
                    "name": "Pro",
                    "price": 20.00,
                    "currency": "USD",
                    "features": ["Unlimited conversations", "Priority support", "API access"]
                }
            ],
            "message": "Available billing plans"
        }

    except Exception as e:
        logger.error(f"Error getting billing plans: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get billing plans"
        )


@router.get("/cost-estimate")
async def estimate_cost(
    service: str = Query(..., description="Service name"),
    usage_type: str = Query(..., description="Usage type"),
    amount: int = Query(..., gt=0, description="Amount of usage"),
    current_user = Depends(get_current_user)
):
    """
    Estimate cost for a given usage amount.
    """
    try:
        # Mock calculation - replace with actual service call
        base_rates = {
            "chat": {"conversations": 0.01, "tokens": 0.0001},
            "api": {"requests": 0.01, "tokens": 0.0001},
            "tts": {"minutes": 0.15, "characters": 0.0001},
            "image": {"generations": 0.02, "pixels": 0.000001}
        }

        rate = base_rates.get(service, {}).get(usage_type, 0.01)
        estimated_cost = amount * rate

        return {
            "service": service,
            "usage_type": usage_type,
            "amount": amount,
            "estimated_cost": round(estimated_cost, 4),
            "currency": "USD",
            "rate_per_unit": rate
        }

    except Exception as e:
        logger.error(f"Error estimating cost: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to estimate cost"
        )
