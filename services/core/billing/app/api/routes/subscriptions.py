"""
Subscription Management API Routes

Handles subscription lifecycle management, plan changes, and billing cycles.

NOTE: Migrated to use PostgreSQL Cluster Service instead of direct database access.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
# from sqlalchemy.orm import Session  # Disabled - using PostgreSQL Cluster Service
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from decimal import Decimal
from uuid import UUID

# from app.models.database import get_db  # Disabled - using PostgreSQL Cluster Service
from app.services.postgresql_client import postgresql_client
from app.api.dependencies import get_current_user
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


# Pydantic Models
class SubscriptionPlanResponse(BaseModel):
    """Response model for subscription plans."""
    id: str
    name: str
    description: str
    price: Decimal
    currency: str
    billing_cycle: str
    features: List[str]
    limits: Dict[str, Any]
    is_popular: bool = False

    class Config:
        from_attributes = True


class SubscriptionCreateRequest(BaseModel):
    """Request model for creating subscriptions."""
    plan_id: str = Field(..., description="Subscription plan ID")
    payment_method_id: Optional[str] = Field(None, description="Payment method ID")
    trial_days: Optional[int] = Field(None, description="Trial period in days")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class SubscriptionResponse(BaseModel):
    """Response model for subscriptions."""
    id: str
    user_id: str
    plan_id: str
    status: str
    current_period_start: datetime
    current_period_end: datetime
    trial_end: Optional[datetime]
    cancel_at_period_end: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class SubscriptionUpdateRequest(BaseModel):
    """Request model for updating subscriptions."""
    plan_id: Optional[str] = Field(None, description="New plan ID")
    cancel_at_period_end: Optional[bool] = Field(None, description="Cancel at period end")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


# Subscription Plans Endpoints
@router.get(
    "/plans",
    response_model=List[SubscriptionPlanResponse],
    tags=["Subscription Plans"],
    summary="Get Available Subscription Plans",
    description="""
    **Get all available subscription plans**

    Returns a list of all active subscription plans with pricing and features.

    **Features:**
    - Multi-currency pricing
    - Feature comparison
    - Usage limits
    - Popular plan highlighting
    """,
    responses={
        200: {"description": "List of subscription plans"},
        500: {"description": "Internal server error"}
    }
)
async def get_subscription_plans():
    """Get all available subscription plans."""
    try:
        # Mock data - replace with actual service call
        plans = [
            {
                "id": "free",
                "name": "Free",
                "description": "Perfect for getting started",
                "price": Decimal("0.00"),
                "currency": "USD",
                "billing_cycle": "monthly",
                "features": [
                    "10 AI conversations per month",
                    "Basic text generation",
                    "Community support"
                ],
                "limits": {
                    "conversations": 10,
                    "tokens": 10000,
                    "api_calls": 100
                },
                "is_popular": False
            },
            {
                "id": "pro",
                "name": "Pro",
                "description": "For power users and professionals",
                "price": Decimal("20.00"),
                "currency": "USD",
                "billing_cycle": "monthly",
                "features": [
                    "Unlimited AI conversations",
                    "Advanced AI models",
                    "Priority support",
                    "API access",
                    "Custom integrations"
                ],
                "limits": {
                    "conversations": -1,
                    "tokens": 1000000,
                    "api_calls": 10000
                },
                "is_popular": True
            },
            {
                "id": "enterprise",
                "name": "Enterprise",
                "description": "For teams and organizations",
                "price": Decimal("100.00"),
                "currency": "USD",
                "billing_cycle": "monthly",
                "features": [
                    "Everything in Pro",
                    "Team management",
                    "Advanced analytics",
                    "Custom models",
                    "Dedicated support",
                    "SLA guarantee"
                ],
                "limits": {
                    "conversations": -1,
                    "tokens": -1,
                    "api_calls": -1
                },
                "is_popular": False
            }
        ]

        return [SubscriptionPlanResponse(**plan) for plan in plans]

    except Exception as e:
        logger.error(f"Error getting subscription plans: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get subscription plans"
        )


@router.get(
    "/plans/{plan_id}",
    response_model=SubscriptionPlanResponse,
    tags=["Subscription Plans"],
    summary="Get Subscription Plan Details",
    description="""
    **Get detailed information about a specific subscription plan**

    Returns comprehensive plan information including pricing, features, and limits.
    """,
    responses={
        200: {"description": "Subscription plan details"},
        404: {"description": "Plan not found"},
        500: {"description": "Internal server error"}
    }
)
async def get_subscription_plan(plan_id: str):
    """Get details for a specific subscription plan."""
    try:
        # Mock data - replace with actual service call
        plans = {
            "free": {
                "id": "free",
                "name": "Free",
                "description": "Perfect for getting started",
                "price": Decimal("0.00"),
                "currency": "USD",
                "billing_cycle": "monthly",
                "features": [
                    "10 AI conversations per month",
                    "Basic text generation",
                    "Community support"
                ],
                "limits": {
                    "conversations": 10,
                    "tokens": 10000,
                    "api_calls": 100
                },
                "is_popular": False
            },
            "pro": {
                "id": "pro",
                "name": "Pro",
                "description": "For power users and professionals",
                "price": Decimal("20.00"),
                "currency": "USD",
                "billing_cycle": "monthly",
                "features": [
                    "Unlimited AI conversations",
                    "Advanced AI models",
                    "Priority support",
                    "API access",
                    "Custom integrations"
                ],
                "limits": {
                    "conversations": -1,
                    "tokens": 1000000,
                    "api_calls": 10000
                },
                "is_popular": True
            }
        }

        if plan_id not in plans:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Plan '{plan_id}' not found"
            )

        return SubscriptionPlanResponse(**plans[plan_id])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting subscription plan {plan_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get subscription plan"
        )


# User Subscription Endpoints
@router.post(
    "/",
    response_model=SubscriptionResponse,
    tags=["Subscriptions"],
    summary="Create New Subscription",
    description="""
    **Create a new subscription for the authenticated user**

    Creates a subscription to a specific plan with optional payment method.

    **Process:**
    1. Validates the subscription plan
    2. Sets up payment method (if provided)
    3. Creates subscription with trial period
    4. Processes initial payment (if no trial)
    5. Returns subscription details

    **Example Request:**
    ```json
    {
        "plan_id": "pro",
        "payment_method_id": "pm_1234567890",
        "trial_days": 14,
        "metadata": {
            "source": "web_app",
            "campaign": "summer_2024"
        }
    }
    ```
    """,
    responses={
        201: {"description": "Subscription created successfully"},
        400: {"description": "Invalid request data"},
        404: {"description": "Plan not found"},
        409: {"description": "User already has active subscription"},
        500: {"description": "Internal server error"}
    }
)
async def create_subscription(
    request: SubscriptionCreateRequest,
    current_user = Depends(get_current_user)
):
    """Create a new subscription for the authenticated user."""
    try:
        # Mock response - replace with actual service call
        subscription_data = {
            "id": "sub_1234567890",
            "user_id": str(current_user.id),
            "plan_id": request.plan_id,
            "status": "active",
            "current_period_start": datetime.utcnow(),
            "current_period_end": datetime.utcnow() + timedelta(days=30),
            "trial_end": datetime.utcnow() + timedelta(days=request.trial_days) if request.trial_days else None,
            "cancel_at_period_end": False,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }

        return SubscriptionResponse(**subscription_data)

    except Exception as e:
        logger.error(f"Error creating subscription: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create subscription"
        )


@router.get(
    "/me",
    response_model=Optional[SubscriptionResponse],
    tags=["Subscriptions"],
    summary="Get Current User's Subscription",
    description="""
    **Get the current user's active subscription**

    Returns the user's current subscription details including:
    - Subscription status and billing cycle
    - Plan information and pricing
    - Trial period information
    - Cancellation status
    """,
    responses={
        200: {"description": "User's subscription details"},
        404: {"description": "No active subscription found"},
        500: {"description": "Internal server error"}
    }
)
async def get_current_subscription(
    current_user = Depends(get_current_user)
):
    """Get the current user's subscription."""
    try:
        # Mock response - replace with actual service call
        subscription_data = {
            "id": "sub_1234567890",
            "user_id": str(current_user.id),
            "plan_id": "pro",
            "status": "active",
            "current_period_start": datetime.utcnow() - timedelta(days=15),
            "current_period_end": datetime.utcnow() + timedelta(days=15),
            "trial_end": None,
            "cancel_at_period_end": False,
            "created_at": datetime.utcnow() - timedelta(days=15),
            "updated_at": datetime.utcnow()
        }

        return SubscriptionResponse(**subscription_data)

    except Exception as e:
        logger.error(f"Error getting user subscription: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get subscription"
        )
