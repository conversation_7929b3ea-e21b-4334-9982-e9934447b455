"""
SimbaAI Billing Service
Payment processing, subscription management, and usage tracking
Technology: FastAPI + Payment Providers + PostgreSQL + Vault
Port: 7309
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, PlainTextResponse
import logging
import os
from pathlib import Path
from contextlib import asynccontextmanager
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from slowapi.middleware import SlowAPIMiddleware
import structlog

# Import route modules
from app.api.routes.billing import router as billing_router
from app.api.routes.subscriptions import router as subscription_router
from app.api.routes.payments.endpoints import router as payment_router
from app.api.routes.webhooks import router as webhook_router

# Import security modules
from app.security.middleware import security_middleware, limiter
from app.security.auth import auth_manager

# Import error handling
from app.core.error_handlers import setup_error_handlers

# Import monitoring - DISABLED: Missing dependencies
# from app.core.monitoring import get_metrics, get_health_check, update_system_metrics_task

# Database management via PostgreSQL Cluster Service
# Direct database access removed - using PostgreSQL Cluster Service instead

# Import Vault integration functions
from app.startup.vault_startup import initialize_vault_for_service as initialize_vault, get_vault_config

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# Get the main.py path for service categorization
main_py_path = Path(__file__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("🚀 Starting Billing Service...")

    # Initialize Vault configuration
    try:
        await initialize_vault()
        logger.info("✅ Vault integration initialized")
    except Exception as e:
        logger.warning(f"⚠️ Vault initialization failed: {e}")

    # Initialize database via PostgreSQL Cluster Service (non-blocking)
    import asyncio

    async def initialize_database_background():
        """Initialize database in background with retries."""
        try:
            logger.info("🔧 Starting background database initialization via PostgreSQL Cluster Service...")
            from app.services.cluster_db_init import initialize_database_via_cluster

            # Retry database initialization with exponential backoff
            max_retries = 10
            base_delay = 2.0

            for attempt in range(max_retries):
                try:
                    logger.info(f"🔄 Database initialization attempt {attempt + 1}/{max_retries}...")
                    success = await initialize_database_via_cluster()
                    if success:
                        logger.info("✅ Billing database initialized successfully via PostgreSQL Cluster Service")
                        return
                    else:
                        logger.warning(f"⚠️ Database initialization attempt {attempt + 1} failed")

                except Exception as e:
                    logger.warning(f"⚠️ Database initialization attempt {attempt + 1} failed: {e}")

                if attempt < max_retries - 1:
                    delay = base_delay * (2 ** attempt)  # Exponential backoff
                    logger.info(f"⏳ Waiting {delay}s before retry...")
                    await asyncio.sleep(delay)

            logger.error("❌ All database initialization attempts failed")

        except Exception as e:
            logger.error(f"❌ Database initialization background task failed: {e}")

    # Start database initialization in background
    asyncio.create_task(initialize_database_background())

    # Initialize inter-service communication and register with service registry
    try:
        from app.services.inter_service_client import billing_service_client
        await billing_service_client.initialize()
        logger.info("✅ Service registry registration completed")
    except Exception as e:
        logger.warning(f"⚠️ Service registry registration failed: {e}")

    # Start background monitoring task - DISABLED: Missing dependencies
    # import asyncio
    # monitoring_task = asyncio.create_task(update_system_metrics_task())
    # logger.info("✅ System monitoring started")

    logger.info("✅ Billing Service started successfully")

    yield

    # Shutdown
    logger.info("🛑 Shutting down Billing Service...")

    # Cancel monitoring task - DISABLED: Missing dependencies
    # monitoring_task.cancel()
    # try:
    #     await monitoring_task
    # except asyncio.CancelledError:
    #     pass
    # logger.info("✅ System monitoring stopped")

    # Database cleanup handled by PostgreSQL Cluster Service
    logger.info("📋 Database cleanup handled by PostgreSQL Cluster Service")

    logger.info("✅ Billing Service shutdown complete")


# Create FastAPI application with comprehensive OpenAPI documentation
app = FastAPI(
    title="SimbaAI Billing Service",
    description="""
    ## 🚀 **SimbaAI Billing Service**

    Comprehensive payment processing, subscription management, and usage tracking service.

    ### **Features**
    - **Multi-Currency Support**: USD, EUR, KES, and 30+ currencies
    - **Payment Providers**: Stripe, PayPal, M-Pesa integration
    - **Subscription Management**: ChatGPT-like plans with configurable features
    - **Usage Tracking**: Real-time usage monitoring and billing
    - **Currency Conversion**: Real-time exchange rates with multiple providers
    - **Email Notifications**: Payment confirmations and invoices
    - **Webhook Support**: Real-time payment status updates
    - **Security**: Rate limiting, authentication, and audit logging

    ### **API Categories**
    - **Subscription Plans**: Manage pricing and features
    - **Subscriptions**: User subscription lifecycle
    - **Payments**: Multi-provider payment processing
    - **Webhooks**: Payment provider notifications
    - **Usage Tracking**: Monitor and bill for usage
    - **Invoicing**: Generate and manage invoices

    ### **Supported Payment Methods**
    - **Credit/Debit Cards** (via Stripe)
    - **PayPal Account** and PayPal Credit
    - **M-Pesa** (Kenya mobile money)
    - **Bank Transfers** and Digital Wallets

    ### **Rate Limiting**
    - Standard endpoints: 100 requests per hour
    - Payment endpoints: 10 requests per minute
    - Currency conversion: 60 requests per hour
    """,
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
    openapi_tags=[
        {
            "name": "Subscription Plans",
            "description": "Manage subscription plans and pricing. Get available plans, plan details, and feature comparisons."
        },
        {
            "name": "Subscriptions",
            "description": "User subscription lifecycle management. Create, update, cancel, and track subscriptions."
        },
        {
            "name": "Payment Providers",
            "description": "Get information about available payment providers and their capabilities."
        },
        {
            "name": "Payments",
            "description": "Process payments with multiple providers. Support for Stripe, PayPal, and M-Pesa."
        },
        {
            "name": "M-Pesa Payments",
            "description": "M-Pesa mobile money payments with STK Push integration for Kenya."
        },
        {
            "name": "Webhooks",
            "description": "Webhook endpoints for payment provider notifications and status updates."
        },
        {
            "name": "Usage Tracking",
            "description": "Track and monitor usage for billing purposes. Feature usage, API calls, and cost calculation."
        },
        {
            "name": "Invoicing",
            "description": "Invoice generation and management. PDF invoices, email delivery, and payment tracking."
        }
    ],
    contact={
        "name": "SimbaAI Support",
        "url": "https://simbaai.com/support",
        "email": "<EMAIL>"
    },
    license_info={
        "name": "SimbaAI License",
        "url": "https://simbaai.com/license"
    }
)

# Add security middleware
app.add_middleware(SlowAPIMiddleware)
app.middleware("http")(security_middleware)

# Add CORS middleware with security considerations
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "https://simbaai.com",
        "https://app.simbaai.com",
        "https://api.simbaai.com",
        "http://localhost:3000",  # Development only
        "http://localhost:8080"   # Development only
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
    allow_headers=["*"],
    expose_headers=["X-Request-ID", "X-Rate-Limit-Remaining"]
)

# Add rate limiting exception handler
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# Setup comprehensive error handlers
setup_error_handlers(app)

# Health endpoint defined below - removing duplicate

# Include routers with proper tags and prefixes
app.include_router(billing_router, prefix="/api/v1/billing", tags=["Usage Tracking", "Invoicing"])
app.include_router(subscription_router, prefix="/api/v1/subscriptions", tags=["Subscription Plans", "Subscriptions"])
app.include_router(payment_router, prefix="/api/v1/payments", tags=["Payment Providers", "Payments", "M-Pesa Payments"])
app.include_router(webhook_router, prefix="/webhooks", tags=["Webhooks"])


@app.get("/health")
async def health_check():
    """Health check endpoint for service discovery"""
    try:
        # Get comprehensive health check
        health_data = await get_health_check()

        # Add service-specific information
        health_data.update({
            "service": "billing",
            "port": 7309,
            "category": "core",
            "technology": "FastAPI + Stripe + PayPal + M-Pesa + PostgreSQL + Vault",
            "vault_status": "connected",
            "gpu_required": False,
            "configuration_source": "vault",
            "features": [
                "Multi-currency payments",
                "Subscription management",
                "Usage tracking",
                "Webhook processing",
                "Invoice generation"
            ]
        })

        # Check database health if available
        try:
            from app.core.database import db_manager
            db_health = await db_manager.health_check()
            health_data["database"] = db_health
        except Exception as db_e:
            logger.warning("Database health check failed", error=str(db_e))
            health_data["database"] = {"status": "unknown", "error": str(db_e)}

        return health_data

    except Exception as e:
        logger.error("Health check failed", error=str(e))
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "service": "billing",
                "error": str(e)
            }
        )


@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    metrics_data = await get_metrics()
    return PlainTextResponse(
        content=metrics_data,
        media_type="text/plain; version=0.0.4; charset=utf-8"
    )


@app.get("/")
async def root():
    """Root endpoint with comprehensive API information"""
    return {
        "message": "SimbaAI Billing Service",
        "version": "2.0.0",
        "service": "billing",
        "port": 7309,
        "status": "production-ready",
        "documentation": {
            "swagger_ui": "/docs",
            "redoc": "/redoc",
            "openapi_json": "/openapi.json"
        },
        "api_categories": {
            "subscription_plans": "/api/v1/subscriptions/plans",
            "subscriptions": "/api/v1/subscriptions",
            "payment_providers": "/api/v1/payments/providers",
            "payments": "/api/v1/payments",
            "mpesa_payments": "/api/v1/payments/mpesa/stk-push",
            "webhooks": "/webhooks",
            "usage_tracking": "/api/v1/billing/usage",
            "invoicing": "/api/v1/billing/invoices"
        },
        "supported_providers": ["stripe", "paypal", "mpesa"],
        "supported_currencies": ["USD", "EUR", "GBP", "KES", "CAD", "AUD"],
        "features": [
            "Multi-currency support",
            "Real-time payment processing",
            "Subscription management",
            "Usage tracking and billing",
            "Webhook notifications",
            "Invoice generation",
            "Rate limiting",
            "Security and authentication"
        ]
    }

# Startup handled by lifespan manager above

if __name__ == "__main__":
    import uvicorn
    import os

    # Get port from environment or default (Vault will override during startup)
    port = int(os.getenv("PORT", "7309"))
    host = os.getenv("HOST", "0.0.0.0")

    uvicorn.run(app, host=host, port=port)
